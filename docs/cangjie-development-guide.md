# 仓颉语言 HarmonyOS 开发环境配置与测试指引

## 1. 环境配置总结

### 1.1 必需软件和工具
- **DevEco Studio 5.0.0 Release** - HarmonyOS 开发IDE
- **DevEco Studio-Cangjie Plugin Beta** - 仓颉语言插件
- **Node.js** (v22.13.0+) - 构建工具依赖
- **ohpm** - HarmonyOS 包管理器 (位于 `/Applications/DevEco-Studio.app/Contents/tools/ohpm/bin/ohpm`)
- **hvigor** - HarmonyOS 构建工具 (位于 `/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/bin/hvigor.js`)

### 1.2 申请和安装流程
1. **申请公测权限**：在华为开发者网站申请仓颉开发者预览版招募报名
2. **下载软件**：从华为开发者下载中心获取 DevEco Studio 和仓颉插件
3. **安装插件**：在 DevEco Studio 中安装仓颉插件并重启
4. **环境变量**：可选设置 `DEVECO_CANGJIE_PATH` 指定 SDK 存放路径

### 1.3 SDK 路径
- **macOS**: `$HOME/.cangjie-sdk`
- **Windows**: `%USERPROFILE%/.cangjie-sdk`

## 2. 项目结构和构建系统

### 2.1 项目结构
```
homework-assistant/
├── oh-package.json5          # HarmonyOS 包配置
├── build-profile.json5       # 构建配置
├── hvigorfile.ts            # 构建脚本
├── hvigor/                  # 构建工具配置
│   ├── hvigor-config.json5
│   └── cangjie-build-support-3.1.128.tgz
├── entry/                   # 应用入口模块
│   ├── src/main/cangjie/    # 仓颉源码
│   │   ├── models/          # 数据模型
│   │   ├── index.cj         # UI入口
│   │   └── main_ability.cj  # 应用能力
│   └── src/test/cangjie/    # 测试代码
└── AppScope/                # 应用范围配置
```

### 2.2 包管理
- **安装依赖**: `ohpm install`
- **包配置文件**: `oh-package.json5` (不是 npm 的 package.json)
- **锁定文件**: `oh-package-lock.json5`

### 2.3 构建命令
```bash
# 编译 HAP 包
node /Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/bin/hvigor.js --mode module -p module=entry@default assembleHap

# 简化命令 (如果配置了环境变量)
hvigor --mode module -p module=entry@default assembleHap
```

## 3. 仓颉语言语法要点

### 3.1 基本语法
- **包声明**: `package ohos_app_cangjie_entry.models`
- **类定义**: `public class ClassName { ... }`
- **变量声明**: `let` (不可变), `var` (可变)
- **函数定义**: `public func functionName(): ReturnType { ... }`

### 3.2 数据类型
- **基本类型**: `String`, `Int64`, `Float64`, `Bool`
- **数组类型**: `Array<T>` (不是 ArrayList)
- **枚举类型**: `enum EnumName { | Value1 | Value2 }`
- **Option类型**: 使用简化的字符串或基本类型替代

### 3.3 数组操作
```cangjie
// 创建数组
let arr = Array<String>(size, {i => initialValue})

// 添加元素 (需要重新创建数组)
let newArr = Array<String>(oldArr.size + 1, {i =>
    if (i < oldArr.size) {
        oldArr[i]
    } else {
        newElement
    }
})
```

### 3.4 Lambda 表达式
```cangjie
// 正确语法 - 需要用大括号包围 if-else
{i =>
    if (condition) {
        value1
    } else {
        value2
    }
}
```

## 4. 常见编译错误和解决方案

### 4.1 语法错误
- **错误**: `expected '{', found keyword 'this'`
- **原因**: Lambda 表达式中的 if-else 需要用大括号包围
- **解决**: 将 `if (condition) value1 else value2` 改为 `if (condition) { value1 } else { value2 }`

### 4.2 包声明冲突
- **错误**: `found more than one package declaration for the package`
- **原因**: 同一包中有不同的包声明
- **解决**: 统一所有文件的包声明，如 `package ohos_app_cangjie_entry.models`

### 4.3 类型未声明
- **错误**: `undeclared type name 'DateTime'`
- **原因**: 使用了未导入或不存在的类型
- **解决**: 使用简化类型如 `String` 替代 `DateTime`，`Array<T>` 替代 `ArrayList<T>`

## 5. 测试框架

### 5.1 测试结构
- **测试目录**: `entry/src/test/cangjie/`
- **测试注解**: `@Test` (类), `@TestCase` (方法)
- **断言**: `@Assert.equal()`, `@Assert.notEqual()` 等

### 5.2 简化测试方法
由于测试框架可能不完整，可以使用简单的条件判断和 `println` 进行基础测试：
```cangjie
public func testBasic(): Unit {
    let result = someFunction()
    if (result != expected) {
        println("Test failed: expected ${expected}, got ${result}")
    } else {
        println("Test passed")
    }
}
```

## 6. 运行和调试

### 6.1 模拟器运行
1. 在 DevEco Studio 中打开 Device Manager
2. 创建新的模拟器实例
3. 下载系统镜像
4. 启动模拟器并运行应用

### 6.2 真机运行
- 支持 USB 连接和无线调试
- 需要在 HarmonyOS 设备上启用开发者模式

## 7. 开发建议

### 7.1 TDD 开发流程
1. **RED**: 先写失败的测试
2. **GREEN**: 实现最简单的代码让测试通过
3. **REFACTOR**: 重构代码提高质量

### 7.2 版本控制
- 每个 TDD 循环都要提交代码
- 使用描述性的提交信息：
  - `RED: 添加测试 - [测试描述]`
  - `GREEN: 实现功能 - [功能描述]`
  - `REFACTOR: 优化代码 - [重构内容]`

### 7.3 当前限制
- 仓颉语言仍在公测阶段，API 可能不完整
- 某些标准库功能可能不可用，需要使用简化实现
- 测试框架可能需要自定义实现
- 文档和示例相对较少，需要通过编译错误来学习语法

## 8. 故障排除

### 8.1 编译失败
1. 检查包声明是否一致
2. 确认所有类型都已正确声明
3. 验证 Lambda 表达式语法
4. 查看编译器错误信息中的具体行号和建议

### 8.2 构建工具问题
1. 确认 ohpm 和 hvigor 路径正确
2. 运行 `ohpm install` 安装依赖
3. 检查 Node.js 版本兼容性

### 8.3 IDE 问题
1. 重启 DevEco Studio
2. 清理项目缓存
3. 重新安装仓颉插件
