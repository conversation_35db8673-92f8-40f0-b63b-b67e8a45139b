# 作业助手APP

基于仓颉语言和HarmonyOS 5开发的AI智能作业辅助应用

## 功能特性

### 🎯 智能听写模块
- AI语音播报听写内容
- 支持拍照OCR识别和手写输入
- 自动批改和错误标注
- 听写报告和错题集

### 📖 智能背诵模块  
- AI实时监听背诵语音
- 准确性和流利度评估
- 即时反馈和纠错指导
- 背诵进度跟踪

## 技术栈

- **编程语言**: 仓颉 (Cangjie)
- **UI框架**: HarmonyOS UI
- **目标平台**: HarmonyOS 5
- **AI能力**: TTS、ASR、OCR、语音评测

## 项目结构

```
homework-assistant/
├── cjpm.toml              # 项目配置文件
├── src/
│   ├── main.cj           # 应用入口
│   ├── models/           # 数据模型
│   ├── ui/               # 用户界面
│   ├── services/         # AI服务
│   └── utils/            # 工具函数
├── tests/                # 测试文件
└── docs/                 # 文档
```

## 开发进度

- [x] 项目初始化
- [ ] 核心数据模型设计
- [ ] 基础UI框架搭建
- [ ] 智能听写模块实现
- [ ] 智能背诵模块实现
- [ ] 数据存储和管理
- [ ] UI优化和用户体验
- [ ] 测试和调试

## 开发环境

1. 安装仓颉开发环境
2. 配置HarmonyOS SDK
3. 使用DevEco Studio或VSCode进行开发

## 许可证

MIT License
