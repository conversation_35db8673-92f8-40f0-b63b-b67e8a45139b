package ohos_app_cangjie_entry.services

/**
 * 服务操作结果
 * 用于统一处理服务层的返回结果
 */
public class ServiceResult {
    public let success: Bool
    public let message: String
    public let errorCode: String
    
    public init(success: Bool, message: String, errorCode: String) {
        this.success = success
        this.message = message
        this.errorCode = errorCode
    }
    
    /**
     * 创建成功结果
     */
    public static func createSuccess(message: String): ServiceResult {
        return ServiceResult(true, message, "")
    }

    /**
     * 创建失败结果
     */
    public static func createFailure(message: String, errorCode: String): ServiceResult {
        return ServiceResult(false, message, errorCode)
    }
}

/**
 * 文字识别结果
 * 用于OCR和手写识别的返回结果
 */
public class RecognitionResult {
    public let success: Bool
    public let text: String
    public let confidence: Float64  // 识别置信度 (0-100)
    public let message: String
    public let errorCode: String
    
    public init(success: Bool, text: String, confidence: Float64, message: String, errorCode: String) {
        this.success = success
        this.text = text
        this.confidence = confidence
        this.message = message
        this.errorCode = errorCode
    }
    
    /**
     * 创建成功的识别结果
     */
    public static func createSuccess(text: String, confidence: Float64): RecognitionResult {
        return RecognitionResult(true, text, confidence, "识别成功", "")
    }

    /**
     * 创建失败的识别结果
     */
    public static func createFailure(message: String, errorCode: String): RecognitionResult {
        return RecognitionResult(false, "", 0.0, message, errorCode)
    }
}

/**
 * 语音播报结果
 * 用于TTS功能的返回结果
 */
public class SpeechResult {
    public let success: Bool
    public let duration: Int64      // 播报时长（毫秒）
    public let message: String
    public let errorCode: String
    
    public init(success: Bool, duration: Int64, message: String, errorCode: String) {
        this.success = success
        this.duration = duration
        this.message = message
        this.errorCode = errorCode
    }
    
    /**
     * 创建成功的语音结果
     */
    public static func createSuccess(duration: Int64): SpeechResult {
        return SpeechResult(true, duration, "播报成功", "")
    }

    /**
     * 创建失败的语音结果
     */
    public static func createFailure(message: String, errorCode: String): SpeechResult {
        return SpeechResult(false, 0, message, errorCode)
    }
}

/**
 * 批改结果
 * 用于自动批改功能的返回结果
 */
public class CorrectionResult {
    public let success: Bool
    public let score: Float64       // 得分 (0-100)
    public let corrections: Array<String>  // 批改详情
    public let message: String
    public let errorCode: String
    
    public init(success: Bool, score: Float64, corrections: Array<String>, message: String, errorCode: String) {
        this.success = success
        this.score = score
        this.corrections = corrections
        this.message = message
        this.errorCode = errorCode
    }
    
    /**
     * 创建成功的批改结果
     */
    public static func createSuccess(score: Float64, corrections: Array<String>): CorrectionResult {
        return CorrectionResult(true, score, corrections, "批改完成", "")
    }

    /**
     * 创建失败的批改结果
     */
    public static func createFailure(message: String, errorCode: String): CorrectionResult {
        let emptyCorrections = Array<String>(0, {i => ""})
        return CorrectionResult(false, 0.0, emptyCorrections, message, errorCode)
    }
}

/**
 * 背诵评估结果
 * 用于背诵评测功能的返回结果
 */
public class RecitationEvaluationResult {
    public let success: Bool
    public let accuracyScore: Float64      // 准确性得分 (0-100)
    public let fluencyScore: Float64       // 流利度得分 (0-100)
    public let completenessScore: Float64  // 完整性得分 (0-100)
    public let overallScore: Float64       // 综合得分 (0-100)
    public let errors: Array<String>       // 错误详情
    public let message: String
    public let errorCode: String

    public init(
        success: Bool,
        accuracyScore: Float64,
        fluencyScore: Float64,
        completenessScore: Float64,
        overallScore: Float64,
        errors: Array<String>,
        message: String,
        errorCode: String
    ) {
        this.success = success
        this.accuracyScore = accuracyScore
        this.fluencyScore = fluencyScore
        this.completenessScore = completenessScore
        this.overallScore = overallScore
        this.errors = errors
        this.message = message
        this.errorCode = errorCode
    }

    /**
     * 创建成功的评估结果
     */
    public static func createSuccess(
        accuracyScore: Float64,
        fluencyScore: Float64,
        completenessScore: Float64,
        errors: Array<String>
    ): RecitationEvaluationResult {
        let overallScore = (accuracyScore + fluencyScore + completenessScore) / 3.0
        return RecitationEvaluationResult(
            true, accuracyScore, fluencyScore, completenessScore, overallScore,
            errors, "评估完成", ""
        )
    }

    /**
     * 创建失败的评估结果
     */
    public static func createFailure(message: String, errorCode: String): RecitationEvaluationResult {
        let emptyErrors = Array<String>(0, {i => ""})
        return RecitationEvaluationResult(
            false, 0.0, 0.0, 0.0, 0.0, emptyErrors, message, errorCode
        )
    }
}
