package ohos_app_cangjie_entry.models

import std.time.DateTime
import std.collection.ArrayList
import std.collection.HashMap

/**
 * 用户档案数据模型
 * 用于管理用户信息、学习记录和偏好设置
 */
public class UserProfile {
    // 基本信息
    public let userId: String
    public var userName: String
    public var grade: String        // 年级
    public var avatar: String       // 头像路径
    public let createdAt: DateTime
    public var updatedAt: DateTime
    
    // 学习统计
    public var totalDictationTasks: Int64      // 总听写任务数
    public var completedDictationTasks: Int64  // 完成的听写任务数
    public var totalRecitationTasks: Int64     // 总背诵任务数
    public var completedRecitationTasks: Int64 // 完成的背诵任务数
    public var totalStudyTime: Int64           // 总学习时长（分钟）
    public var consecutiveStudyDays: Int64     // 连续学习天数
    public var lastStudyDate: Option<DateTime> // 最后学习日期
    
    // 成绩统计
    public var averageDictationScore: Float64  // 平均听写得分
    public var averageRecitationScore: Float64 // 平均背诵得分
    public var bestDictationScore: Float64     // 最佳听写得分
    public var bestRecitationScore: Float64    // 最佳背诵得分
    
    // 偏好设置
    public var preferences: UserPreferences
    
    // 学习记录
    public var studyHistory: ArrayList<StudySession>  // 学习会话记录
    public var achievements: ArrayList<Achievement>   // 成就记录
    
    public init(userId: String, userName: String, grade: String) {
        this.userId = userId
        this.userName = userName
        this.grade = grade
        this.avatar = ""
        this.createdAt = DateTime.now()
        this.updatedAt = DateTime.now()
        
        // 初始化统计数据
        this.totalDictationTasks = 0
        this.completedDictationTasks = 0
        this.totalRecitationTasks = 0
        this.completedRecitationTasks = 0
        this.totalStudyTime = 0
        this.consecutiveStudyDays = 0
        this.lastStudyDate = None
        
        this.averageDictationScore = 0.0
        this.averageRecitationScore = 0.0
        this.bestDictationScore = 0.0
        this.bestRecitationScore = 0.0
        
        this.preferences = UserPreferences()
        this.studyHistory = ArrayList<StudySession>()
        this.achievements = ArrayList<Achievement>()
    }
    
    /**
     * 更新听写统计
     */
    public func updateDictationStats(score: Float64, isCompleted: Bool): Unit {
        this.totalDictationTasks += 1
        if (isCompleted) {
            this.completedDictationTasks += 1
        }
        
        if (score > this.bestDictationScore) {
            this.bestDictationScore = score
        }
        
        // 重新计算平均分
        this.recalculateAverageScores()
        this.updatedAt = DateTime.now()
    }
    
    /**
     * 更新背诵统计
     */
    public func updateRecitationStats(score: Float64, isCompleted: Bool): Unit {
        this.totalRecitationTasks += 1
        if (isCompleted) {
            this.completedRecitationTasks += 1
        }
        
        if (score > this.bestRecitationScore) {
            this.bestRecitationScore = score
        }
        
        // 重新计算平均分
        this.recalculateAverageScores()
        this.updatedAt = DateTime.now()
    }
    
    /**
     * 添加学习会话记录
     */
    public func addStudySession(session: StudySession): Unit {
        this.studyHistory.append(session)
        this.totalStudyTime += session.duration
        
        // 更新连续学习天数
        this.updateConsecutiveStudyDays(session.date)
        this.lastStudyDate = Some(session.date)
        this.updatedAt = DateTime.now()
    }
    
    /**
     * 获取学习完成率
     */
    public func getDictationCompletionRate(): Float64 {
        if (this.totalDictationTasks == 0) {
            return 0.0
        }
        return (this.completedDictationTasks.toFloat64() / this.totalDictationTasks.toFloat64()) * 100.0
    }
    
    public func getRecitationCompletionRate(): Float64 {
        if (this.totalRecitationTasks == 0) {
            return 0.0
        }
        return (this.completedRecitationTasks.toFloat64() / this.totalRecitationTasks.toFloat64()) * 100.0
    }
    
    /**
     * 重新计算平均分
     */
    private func recalculateAverageScores(): Unit {
        // 这里需要从历史记录中重新计算，简化实现
        // 实际应用中应该从数据库查询所有任务记录
    }
    
    /**
     * 更新连续学习天数
     */
    private func updateConsecutiveStudyDays(currentDate: DateTime): Unit {
        // 简化实现，实际需要比较日期
        this.consecutiveStudyDays += 1
    }
}

/**
 * 用户偏好设置
 */
public class UserPreferences {
    public var speechSpeed: Float64        // 语音播放速度 (0.5-2.0)
    public var speechVolume: Float64       // 语音音量 (0.0-1.0)
    public var voiceType: String          // 语音类型
    public var enableSoundEffects: Bool   // 是否启用音效
    public var enableVibration: Bool      // 是否启用震动
    public var autoSaveProgress: Bool     // 是否自动保存进度
    public var reminderEnabled: Bool      // 是否启用学习提醒
    public var reminderTime: String       // 提醒时间
    
    public init() {
        this.speechSpeed = 1.0
        this.speechVolume = 0.8
        this.voiceType = "standard"
        this.enableSoundEffects = true
        this.enableVibration = true
        this.autoSaveProgress = true
        this.reminderEnabled = false
        this.reminderTime = "19:00"
    }
}

/**
 * 学习会话记录
 */
public class StudySession {
    public let sessionId: String
    public let userId: String
    public let date: DateTime
    public let duration: Int64        // 学习时长（分钟）
    public let taskType: TaskType     // 任务类型
    public let tasksCompleted: Int64  // 完成的任务数
    public let averageScore: Float64  // 平均得分
    
    public init(sessionId: String, userId: String, date: DateTime, duration: Int64, taskType: TaskType, tasksCompleted: Int64, averageScore: Float64) {
        this.sessionId = sessionId
        this.userId = userId
        this.date = date
        this.duration = duration
        this.taskType = taskType
        this.tasksCompleted = tasksCompleted
        this.averageScore = averageScore
    }
}

/**
 * 任务类型枚举
 */
public enum TaskType {
    | Dictation    // 听写
    | Recitation   // 背诵
    | Mixed        // 混合
}

/**
 * 成就记录
 */
public class Achievement {
    public let achievementId: String
    public let title: String
    public let description: String
    public let iconPath: String
    public let unlockedAt: DateTime
    public let category: AchievementCategory
    
    public init(achievementId: String, title: String, description: String, iconPath: String, category: AchievementCategory) {
        this.achievementId = achievementId
        this.title = title
        this.description = description
        this.iconPath = iconPath
        this.unlockedAt = DateTime.now()
        this.category = category
    }
}

/**
 * 成就类别枚举
 */
public enum AchievementCategory {
    | StudyTime      // 学习时长类
    | TaskCompletion // 任务完成类
    | ScoreRecord    // 成绩记录类
    | Consistency    // 坚持学习类
    | Special        // 特殊成就类
}
