package ohos_app_cangjie_entry.models

// 注意：这里使用简化的数据类型，实际开发中需要根据仓颉标准库的具体API调整
// import std.time.DateTime
// import std.collection.ArrayList

/**
 * 听写任务数据模型
 * 用于管理听写练习的内容、状态和结果
 */
public class DictationTask {
    // 任务基本信息
    public let id: String
    public let title: String
    public var content: Array<String>  // 听写内容列表
    public let createdAt: String       // 简化为字符串格式
    public var updatedAt: String

    // 任务状态
    public var status: DictationStatus
    public var currentIndex: Int64     // 当前听写到第几个词/句

    // 听写结果
    public var userAnswers: Array<String>  // 用户答案
    public var corrections: Array<DictationCorrection>  // 批改结果
    public var score: Float64          // 得分 (0-100)
    public var completedAt: String     // 完成时间，空字符串表示未完成
    
    public init(id: String, title: String, content: Array<String>) {
        this.id = id
        this.title = title
        this.content = content
        this.createdAt = "2025-07-02T10:00:00"  // 简化为字符串
        this.updatedAt = "2025-07-02T10:00:00"
        this.status = DictationStatus.NotStarted
        this.currentIndex = 0
        this.userAnswers = Array<String>(0, {i => ""})  // 初始化空数组
        this.corrections = Array<DictationCorrection>(0, {i => DictationCorrection(0, "", "", false, Array<String>(0, {j => ""}))})
        this.score = 0.0
        this.completedAt = ""  // 空字符串表示未完成
    }
    
    /**
     * 开始听写任务
     */
    public func start(): Unit {
        this.status = DictationStatus.InProgress
        this.updatedAt = "2025-07-02T10:00:00"  // 简化为字符串
    }
    
    /**
     * 提交当前词语的答案
     */
    public func submitAnswer(answer: String): Unit {
        if (this.currentIndex < this.content.size) {
            // 简化实现：重新创建数组添加元素
            let newAnswers = Array<String>(this.userAnswers.size + 1, {i =>
                if (i < this.userAnswers.size) {
                    this.userAnswers[i]
                } else {
                    answer
                }
            })
            this.userAnswers = newAnswers
            this.currentIndex += 1
            this.updatedAt = "2025-07-02T10:00:00"

            // 如果是最后一个词语，标记为待批改
            if (this.currentIndex >= this.content.size) {
                this.status = DictationStatus.PendingReview
            }
        }
    }
    
    /**
     * 完成批改
     */
    public func completeReview(corrections: Array<DictationCorrection>, score: Float64): Unit {
        this.corrections = corrections
        this.score = score
        this.status = DictationStatus.Completed
        this.completedAt = "2025-07-02T10:00:00"  // 设置完成时间
        this.updatedAt = "2025-07-02T10:00:00"
    }
    
    /**
     * 获取当前需要听写的内容
     */
    public func getCurrentContent(): String {
        if (this.currentIndex < this.content.size) {
            return this.content[this.currentIndex]
        }
        return ""  // 返回空字符串表示没有内容
    }
    
    /**
     * 获取进度百分比
     */
    public func getProgress(): Float64 {
        if (this.content.size == 0) {
            return 0.0
        }
        return (this.currentIndex.toFloat64() / this.content.size.toFloat64()) * 100.0
    }
    
    /**
     * 检查是否可以继续听写
     */
    public func canContinue(): Bool {
        return this.status == DictationStatus.InProgress && this.currentIndex < this.content.size
    }
}

/**
 * 听写任务状态枚举
 */
public enum DictationStatus {
    | NotStarted    // 未开始
    | InProgress    // 进行中
    | PendingReview // 待批改
    | Completed     // 已完成
    | Cancelled     // 已取消
}

/**
 * 听写批改结果
 */
public class DictationCorrection {
    public let index: Int64           // 题目索引
    public let expected: String       // 正确答案
    public let actual: String         // 用户答案
    public let isCorrect: Bool        // 是否正确
    public let errors: Array<String>  // 错误详情

    public init(index: Int64, expected: String, actual: String, isCorrect: Bool, errors: Array<String>) {
        this.index = index
        this.expected = expected
        this.actual = actual
        this.isCorrect = isCorrect
        this.errors = errors
    }
}
