package ohos_app_cangjie_entry.models

// 注意：根据仓颉语言实际语法调整，使用简化的数据类型

/**
 * 背诵任务数据模型
 * 用于管理背诵练习的内容、评估和结果
 */
public class RecitationTask {
    // 任务基本信息
    public let id: String
    public let title: String
    public let content: String  // 背诵内容（完整文本）
    public let contentType: RecitationType  // 内容类型
    public let createdAt: String       // 简化为字符串
    public var updatedAt: String

    // 任务状态
    public var status: RecitationStatus
    public var attempts: Array<RecitationAttempt>  // 背诵尝试记录
    public var bestScore: Float64  // 最佳得分
    
    public init(id: String, title: String, content: String, contentType: RecitationType) {
        this.id = id
        this.title = title
        this.content = content
        this.contentType = contentType
        this.createdAt = "2025-07-02T10:00:00"  // 简化为字符串
        this.updatedAt = "2025-07-02T10:00:00"
        this.status = RecitationStatus.NotStarted
        this.attempts = Array<RecitationAttempt>(0, {i => RecitationAttempt("", "", "", "", 0, 0.0, 0.0, 0.0, "", Array<RecitationError>(0, {j => RecitationError(RecitationErrorType.MissingWord, 0, "", "", "")}), "")})
        this.bestScore = 0.0
    }
    
    /**
     * 开始背诵任务
     */
    public func start(): Unit {
        this.status = RecitationStatus.InProgress
        this.updatedAt = "2025-07-02T10:00:00"
    }
    
    /**
     * 添加背诵尝试记录
     */
    public func addAttempt(attempt: RecitationAttempt): Unit {
        // 简化实现：重新创建数组添加元素
        let newAttempts = Array<RecitationAttempt>(this.attempts.size + 1, {i =>
            if (i < this.attempts.size) {
                this.attempts[i]
            } else {
                attempt
            }
        })
        this.attempts = newAttempts
        this.updatedAt = "2025-07-02T10:00:00"
        
        // 更新最佳得分
        if (attempt.overallScore > this.bestScore) {
            this.bestScore = attempt.overallScore
        }
        
        // 如果达到优秀标准，标记为完成
        if (attempt.overallScore >= 85.0) {
            this.status = RecitationStatus.Completed
        }
    }
    
    /**
     * 获取尝试次数
     */
    public func getAttemptCount(): Int64 {
        return this.attempts.size
    }
    
    /**
     * 获取最近一次尝试
     */
    public func getLatestAttempt(): Option<RecitationAttempt> {
        if (this.attempts.size > 0) {
            return Some(this.attempts[this.attempts.size - 1])
        }
        return None
    }
    
    /**
     * 获取平均得分
     */
    public func getAverageScore(): Float64 {
        if (this.attempts.size == 0) {
            return 0.0
        }
        
        var totalScore = 0.0
        for (attempt in this.attempts) {
            totalScore += attempt.overallScore
        }
        return totalScore / Float64(this.attempts.size)
    }
    
    /**
     * 检查是否需要继续练习
     */
    public func needsMorePractice(): Bool {
        return this.bestScore < 85.0
    }
}

/**
 * 背诵类型枚举
 */
public enum RecitationType {
    | ChinesePoetry    // 中文古诗
    | ChineseText      // 中文课文
    | EnglishText      // 英文课文
    | EnglishWords     // 英文单词
}

/**
 * 背诵状态枚举
 */
public enum RecitationStatus {
    | NotStarted    // 未开始
    | InProgress    // 进行中
    | Completed     // 已完成
    | Cancelled     // 已取消
}

/**
 * 背诵尝试记录
 */
public class RecitationAttempt {
    public let attemptId: String
    public let taskId: String
    public let startTime: String         // 简化为字符串
    public let endTime: String
    public let duration: Int64  // 背诵时长（秒）

    // 评估结果
    public let accuracyScore: Float64    // 准确性得分 (0-100)
    public let fluencyScore: Float64     // 流利度得分 (0-100)
    public let completenessScore: Float64 // 完整性得分 (0-100)
    public let overallScore: Float64     // 综合得分 (0-100)

    // 详细分析
    public let recognizedText: String    // 识别出的文本
    public let errors: Array<RecitationError>  // 错误详情
    public let feedback: String          // 反馈建议
    
    public init(
        attemptId: String,
        taskId: String,
        startTime: String,
        endTime: String,
        duration: Int64,
        accuracyScore: Float64,
        fluencyScore: Float64,
        completenessScore: Float64,
        recognizedText: String,
        errors: Array<RecitationError>,
        feedback: String
    ) {
        this.attemptId = attemptId
        this.taskId = taskId
        this.startTime = startTime
        this.endTime = endTime
        this.duration = duration
        this.accuracyScore = accuracyScore
        this.fluencyScore = fluencyScore
        this.completenessScore = completenessScore
        this.overallScore = (accuracyScore + fluencyScore + completenessScore) / 3.0
        this.recognizedText = recognizedText
        this.errors = errors
        this.feedback = feedback
    }
}

/**
 * 背诵错误类型
 */
public class RecitationError {
    public let errorType: RecitationErrorType
    public let position: Int64      // 错误位置
    public let expected: String     // 期望内容
    public let actual: String       // 实际内容
    public let description: String  // 错误描述
    
    public init(errorType: RecitationErrorType, position: Int64, expected: String, actual: String, description: String) {
        this.errorType = errorType
        this.position = position
        this.expected = expected
        this.actual = actual
        this.description = description
    }
}

/**
 * 背诵错误类型枚举
 */
public enum RecitationErrorType {
    | MissingWord     // 漏词
    | WrongWord       // 错词
    | ExtraWord       // 多词
    | Mispronunciation // 发音错误
    | PauseTooLong    // 停顿过长
    | TooFast         // 语速过快
    | TooSlow         // 语速过慢
}
