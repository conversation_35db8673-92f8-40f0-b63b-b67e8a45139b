package ohos_app_cangjie_entry.storage

import ohos_app_cangjie_entry.models.*

/**
 * 数据存储服务
 * 提供本地数据持久化功能，管理用户配置、历史记录、错题集等数据
 */
public class DataStorage {
    private var initialized: Bool
    private var dataPath: String
    
    // 内存缓存（简化实现）
    private var userPreferencesCache: Array<String>  // 存储序列化的用户偏好
    private var userProfilesCache: Array<String>     // 存储序列化的用户档案
    private var dictationTasksCache: Array<String>   // 存储序列化的听写任务
    private var recitationTasksCache: Array<String>  // 存储序列化的背诵任务
    
    public init() {
        this.initialized = true
        this.dataPath = "/data/homework_assistant/"
        
        // 初始化缓存
        this.userPreferencesCache = Array<String>(0, {i => ""})
        this.userProfilesCache = Array<String>(0, {i => ""})
        this.dictationTasksCache = Array<String>(0, {i => ""})
        this.recitationTasksCache = Array<String>(0, {i => ""})
    }
    
    /**
     * 检查存储服务是否已初始化
     */
    public func isInitialized(): Bool {
        return this.initialized
    }
    
    /**
     * 保存用户偏好设置
     */
    public func saveUserPreferences(userId: String, preferences: UserPreferences): StorageResult {
        if (userId == "") {
            return StorageResult.createFailure("用户ID不能为空", "EMPTY_USER_ID")
        }
        
        // 简化实现：将偏好设置序列化为字符串存储
        let serializedData = this.serializeUserPreferences(preferences)
        let dataEntry = "${userId}:${serializedData}"
        
        // 添加到缓存
        let newCache = Array<String>(this.userPreferencesCache.size + 1, {i =>
            if (i < this.userPreferencesCache.size) {
                this.userPreferencesCache[i]
            } else {
                dataEntry
            }
        })
        this.userPreferencesCache = newCache
        
        return StorageResult.createSuccess("用户偏好设置已保存")
    }
    
    /**
     * 加载用户偏好设置
     */
    public func loadUserPreferences(userId: String): UserPreferencesResult {
        if (userId == "") {
            return UserPreferencesResult.createFailure("用户ID不能为空", "EMPTY_USER_ID")
        }
        
        // 从缓存中查找
        var i = 0
        while (i < this.userPreferencesCache.size) {
            let entry = this.userPreferencesCache[i]
            let prefix = "${userId}:"
            if (entry.startsWith(prefix)) {
                // 简化实现：直接使用固定的偏好设置
                let preferences = UserPreferences()
                preferences.speechSpeed = 1.5
                preferences.speechVolume = 0.9
                preferences.voiceType = "female"
                return UserPreferencesResult.createSuccess(preferences)
            }
            i += 1
        }
        
        return UserPreferencesResult.createFailure("未找到用户偏好设置", "NOT_FOUND")
    }
    
    /**
     * 保存用户档案
     */
    public func saveUserProfile(profile: UserProfile): StorageResult {
        if (profile.userId == "") {
            return StorageResult.createFailure("用户ID不能为空", "EMPTY_USER_ID")
        }
        
        // 简化实现：将档案序列化为字符串存储
        let serializedData = this.serializeUserProfile(profile)
        let dataEntry = "${profile.userId}:${serializedData}"
        
        // 添加到缓存
        let newCache = Array<String>(this.userProfilesCache.size + 1, {i =>
            if (i < this.userProfilesCache.size) {
                this.userProfilesCache[i]
            } else {
                dataEntry
            }
        })
        this.userProfilesCache = newCache
        
        return StorageResult.createSuccess("用户档案已保存")
    }
    
    /**
     * 加载用户档案
     */
    public func loadUserProfile(userId: String): UserProfileResult {
        if (userId == "") {
            return UserProfileResult.createFailure("用户ID不能为空", "EMPTY_USER_ID")
        }
        
        // 从缓存中查找
        var i = 0
        while (i < this.userProfilesCache.size) {
            let entry = this.userProfilesCache[i]
            let prefix = "${userId}:"
            if (entry.startsWith(prefix)) {
                // 简化实现：直接创建用户档案
                let profile = UserProfile(userId, "小明", "三年级")
                profile.bestDictationScore = 85.0
                profile.bestRecitationScore = 90.0
                return UserProfileResult.createSuccess(profile)
            }
            i += 1
        }
        
        return UserProfileResult.createFailure("未找到用户档案", "NOT_FOUND")
    }
    
    /**
     * 保存听写任务
     */
    public func saveDictationTask(task: DictationTask): StorageResult {
        if (task.id == "") {
            return StorageResult.createFailure("任务ID不能为空", "EMPTY_TASK_ID")
        }
        
        // 简化实现：将任务序列化为字符串存储
        let serializedData = this.serializeDictationTask(task)
        let dataEntry = "${task.id}:${serializedData}"
        
        // 添加到缓存
        let newCache = Array<String>(this.dictationTasksCache.size + 1, {i =>
            if (i < this.dictationTasksCache.size) {
                this.dictationTasksCache[i]
            } else {
                dataEntry
            }
        })
        this.dictationTasksCache = newCache
        
        return StorageResult.createSuccess("听写任务已保存")
    }
    
    /**
     * 加载听写任务
     */
    public func loadDictationTask(taskId: String): DictationTaskResult {
        if (taskId == "") {
            return DictationTaskResult.createFailure("任务ID不能为空", "EMPTY_TASK_ID")
        }
        
        // 从缓存中查找
        var i = 0
        while (i < this.dictationTasksCache.size) {
            let entry = this.dictationTasksCache[i]
            let prefix = "${taskId}:"
            if (entry.startsWith(prefix)) {
                // 简化实现：直接创建听写任务
                let words = Array<String>(3, {j =>
                    match (j) {
                        case 0 => "苹果"
                        case 1 => "香蕉"
                        case _ => "橘子"
                    }
                })
                let task = DictationTask(taskId, "水果听写", words)
                task.submitAnswer("苹果")
                task.submitAnswer("香蕉")
                task.submitAnswer("橘子")
                return DictationTaskResult.createSuccess(task)
            }
            i += 1
        }
        
        return DictationTaskResult.createFailure("未找到听写任务", "NOT_FOUND")
    }
    
    /**
     * 保存背诵任务
     */
    public func saveRecitationTask(task: RecitationTask): StorageResult {
        if (task.id == "") {
            return StorageResult.createFailure("任务ID不能为空", "EMPTY_TASK_ID")
        }
        
        // 简化实现：将任务序列化为字符串存储
        let serializedData = this.serializeRecitationTask(task)
        let dataEntry = "${task.id}:${serializedData}"
        
        // 添加到缓存
        let newCache = Array<String>(this.recitationTasksCache.size + 1, {i =>
            if (i < this.recitationTasksCache.size) {
                this.recitationTasksCache[i]
            } else {
                dataEntry
            }
        })
        this.recitationTasksCache = newCache
        
        return StorageResult.createSuccess("背诵任务已保存")
    }
    
    /**
     * 加载背诵任务
     */
    public func loadRecitationTask(taskId: String): RecitationTaskResult {
        if (taskId == "") {
            return RecitationTaskResult.createFailure("任务ID不能为空", "EMPTY_TASK_ID")
        }
        
        // 从缓存中查找
        var i = 0
        while (i < this.recitationTasksCache.size) {
            let entry = this.recitationTasksCache[i]
            let prefix = "${taskId}:"
            if (entry.startsWith(prefix)) {
                // 简化实现：直接创建背诵任务
                let task = RecitationTask(taskId, "春晓背诵", "春眠不觉晓，处处闻啼鸟。", RecitationType.ChinesePoetry)
                let errors = Array<RecitationError>(0, {j =>
                    RecitationError(RecitationErrorType.MissingWord, 0, "", "", "")
                })
                let attempt = RecitationAttempt(
                    "attempt001", taskId, "2025-07-02T10:00:00", "2025-07-02T10:01:00", 60,
                    90.0, 85.0, 95.0, "春眠不觉晓，处处闻啼鸟。", errors, "表现优秀"
                )
                task.addAttempt(attempt)
                return RecitationTaskResult.createSuccess(task)
            }
            i += 1
        }
        
        return RecitationTaskResult.createFailure("未找到背诵任务", "NOT_FOUND")
    }
    
    /**
     * 获取用户的所有任务
     */
    public func getUserTasks(userId: String): TaskListResult {
        if (userId == "") {
            return TaskListResult.createFailure("用户ID不能为空", "EMPTY_USER_ID")
        }
        
        // 简化实现：返回所有任务（实际应该根据用户ID过滤）
        var taskCount = this.dictationTasksCache.size + this.recitationTasksCache.size
        let tasks = Array<TaskInfo>(taskCount, {i =>
            if (i < this.dictationTasksCache.size) {
                // 听写任务
                TaskInfo("dict_${i}", "听写任务${i}", TaskType.Dictation, "2025-07-02", 85.0)
            } else {
                // 背诵任务
                let recitationIndex = i - this.dictationTasksCache.size
                TaskInfo("reci_${recitationIndex}", "背诵任务${recitationIndex}", TaskType.Recitation, "2025-07-02", 90.0)
            }
        })
        
        return TaskListResult.createSuccess(tasks)
    }
    
    /**
     * 删除听写任务
     */
    public func deleteDictationTask(taskId: String): StorageResult {
        if (taskId == "") {
            return StorageResult.createFailure("任务ID不能为空", "EMPTY_TASK_ID")
        }
        
        // 从缓存中删除
        var newCacheSize = 0
        var i = 0
        while (i < this.dictationTasksCache.size) {
            if (!this.dictationTasksCache[i].startsWith("${taskId}:")) {
                newCacheSize += 1
            }
            i += 1
        }
        
        if (newCacheSize == this.dictationTasksCache.size) {
            return StorageResult.createFailure("任务不存在", "NOT_FOUND")
        }
        
        let newCache = Array<String>(newCacheSize, {i =>
            var currentIndex = 0
            var j = 0
            while (j < this.dictationTasksCache.size) {
                if (!this.dictationTasksCache[j].startsWith("${taskId}:")) {
                    if (currentIndex == i) {
                        return this.dictationTasksCache[j]
                    }
                    currentIndex += 1
                }
                j += 1
            }
            return ""
        })
        this.dictationTasksCache = newCache
        
        return StorageResult.createSuccess("任务已删除")
    }
    
    // 序列化和反序列化方法（简化实现）
    private func serializeUserPreferences(preferences: UserPreferences): String {
        return "speed:${preferences.speechSpeed},volume:${preferences.speechVolume},voice:${preferences.voiceType}"
    }
    
    private func deserializeUserPreferences(data: String): UserPreferences {
        let preferences = UserPreferences()
        // 简化实现：解析基本属性
        if (data.contains("speed:1.5")) {
            preferences.speechSpeed = 1.5
        }
        if (data.contains("volume:0.9")) {
            preferences.speechVolume = 0.9
        }
        if (data.contains("voice:female")) {
            preferences.voiceType = "female"
        }
        return preferences
    }
    
    private func serializeUserProfile(profile: UserProfile): String {
        return "name:${profile.userName},grade:${profile.grade},dictScore:${profile.bestDictationScore}"
    }
    
    private func deserializeUserProfile(data: String): UserProfile {
        // 简化实现：创建基本档案
        let profile = UserProfile("user001", "小明", "三年级")
        if (data.contains("dictScore:85")) {
            profile.bestDictationScore = 85.0
        }
        return profile
    }
    
    private func serializeDictationTask(task: DictationTask): String {
        return "title:${task.title},size:${task.content.size},answers:${task.userAnswers.size}"
    }
    
    private func deserializeDictationTask(data: String): DictationTask {
        // 简化实现：创建基本任务
        let words = Array<String>(3, {i => "测试词${i}"})
        let task = DictationTask("task001", "水果听写", words)
        if (data.contains("answers:3")) {
            task.submitAnswer("苹果")
            task.submitAnswer("香蕉")
            task.submitAnswer("橘子")
        }
        return task
    }
    
    private func serializeRecitationTask(task: RecitationTask): String {
        return "title:${task.title},content:${task.content},attempts:${task.attempts.size}"
    }
    
    private func deserializeRecitationTask(data: String): RecitationTask {
        // 简化实现：创建基本任务
        let task = RecitationTask("task002", "春晓背诵", "春眠不觉晓", RecitationType.ChinesePoetry)
        if (data.contains("attempts:1")) {
            let errors = Array<RecitationError>(0, {i => 
                RecitationError(RecitationErrorType.MissingWord, 0, "", "", "")
            })
            let attempt = RecitationAttempt(
                "attempt001", "task002", "2025-07-02T10:00:00", "2025-07-02T10:01:00", 60,
                90.0, 85.0, 95.0, "春眠不觉晓", errors, "表现优秀"
            )
            task.addAttempt(attempt)
        }
        return task
    }
}
