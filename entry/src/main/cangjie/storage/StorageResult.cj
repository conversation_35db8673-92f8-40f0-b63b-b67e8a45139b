package ohos_app_cangjie_entry.storage

import ohos_app_cangjie_entry.models.*

/**
 * 存储操作结果
 * 用于统一处理存储层的返回结果
 */
public class StorageResult {
    public let success: Bool
    public let message: String
    public let errorCode: String
    
    public init(success: Bool, message: String, errorCode: String) {
        this.success = success
        this.message = message
        this.errorCode = errorCode
    }
    
    /**
     * 创建成功结果
     */
    public static func createSuccess(message: String): StorageResult {
        return StorageResult(true, message, "")
    }
    
    /**
     * 创建失败结果
     */
    public static func createFailure(message: String, errorCode: String): StorageResult {
        return StorageResult(false, message, errorCode)
    }
}

/**
 * 用户偏好设置加载结果
 */
public class UserPreferencesResult {
    public let success: Bool
    public let preferences: UserPreferences
    public let message: String
    public let errorCode: String
    
    public init(success: Bool, preferences: UserPreferences, message: String, errorCode: String) {
        this.success = success
        this.preferences = preferences
        this.message = message
        this.errorCode = errorCode
    }
    
    /**
     * 创建成功结果
     */
    public static func createSuccess(preferences: UserPreferences): UserPreferencesResult {
        return UserPreferencesResult(true, preferences, "加载成功", "")
    }
    
    /**
     * 创建失败结果
     */
    public static func createFailure(message: String, errorCode: String): UserPreferencesResult {
        let defaultPreferences = UserPreferences()
        return UserPreferencesResult(false, defaultPreferences, message, errorCode)
    }
}

/**
 * 用户档案加载结果
 */
public class UserProfileResult {
    public let success: Bool
    public let profile: UserProfile
    public let message: String
    public let errorCode: String
    
    public init(success: Bool, profile: UserProfile, message: String, errorCode: String) {
        this.success = success
        this.profile = profile
        this.message = message
        this.errorCode = errorCode
    }
    
    /**
     * 创建成功结果
     */
    public static func createSuccess(profile: UserProfile): UserProfileResult {
        return UserProfileResult(true, profile, "加载成功", "")
    }
    
    /**
     * 创建失败结果
     */
    public static func createFailure(message: String, errorCode: String): UserProfileResult {
        let defaultProfile = UserProfile("", "", "")
        return UserProfileResult(false, defaultProfile, message, errorCode)
    }
}

/**
 * 听写任务加载结果
 */
public class DictationTaskResult {
    public let success: Bool
    public let task: DictationTask
    public let message: String
    public let errorCode: String
    
    public init(success: Bool, task: DictationTask, message: String, errorCode: String) {
        this.success = success
        this.task = task
        this.message = message
        this.errorCode = errorCode
    }
    
    /**
     * 创建成功结果
     */
    public static func createSuccess(task: DictationTask): DictationTaskResult {
        return DictationTaskResult(true, task, "加载成功", "")
    }
    
    /**
     * 创建失败结果
     */
    public static func createFailure(message: String, errorCode: String): DictationTaskResult {
        let emptyContent = Array<String>(0, {i => ""})
        let defaultTask = DictationTask("", "", emptyContent)
        return DictationTaskResult(false, defaultTask, message, errorCode)
    }
}

/**
 * 背诵任务加载结果
 */
public class RecitationTaskResult {
    public let success: Bool
    public let task: RecitationTask
    public let message: String
    public let errorCode: String
    
    public init(success: Bool, task: RecitationTask, message: String, errorCode: String) {
        this.success = success
        this.task = task
        this.message = message
        this.errorCode = errorCode
    }
    
    /**
     * 创建成功结果
     */
    public static func createSuccess(task: RecitationTask): RecitationTaskResult {
        return RecitationTaskResult(true, task, "加载成功", "")
    }
    
    /**
     * 创建失败结果
     */
    public static func createFailure(message: String, errorCode: String): RecitationTaskResult {
        let defaultTask = RecitationTask("", "", "", RecitationType.ChineseText)
        return RecitationTaskResult(false, defaultTask, message, errorCode)
    }
}

/**
 * 任务列表加载结果
 */
public class TaskListResult {
    public let success: Bool
    public let tasks: Array<TaskInfo>
    public let message: String
    public let errorCode: String
    
    public init(success: Bool, tasks: Array<TaskInfo>, message: String, errorCode: String) {
        this.success = success
        this.tasks = tasks
        this.message = message
        this.errorCode = errorCode
    }
    
    /**
     * 创建成功结果
     */
    public static func createSuccess(tasks: Array<TaskInfo>): TaskListResult {
        return TaskListResult(true, tasks, "加载成功", "")
    }
    
    /**
     * 创建失败结果
     */
    public static func createFailure(message: String, errorCode: String): TaskListResult {
        let emptyTasks = Array<TaskInfo>(0, {i => TaskInfo("", "", TaskType.Dictation, "", 0.0)})
        return TaskListResult(false, emptyTasks, message, errorCode)
    }
}

/**
 * 任务信息
 * 用于任务列表显示
 */
public class TaskInfo {
    public let taskId: String
    public let title: String
    public let taskType: TaskType
    public let createdAt: String
    public let score: Float64
    
    public init(taskId: String, title: String, taskType: TaskType, createdAt: String, score: Float64) {
        this.taskId = taskId
        this.title = title
        this.taskType = taskType
        this.createdAt = createdAt
        this.score = score
    }
}
