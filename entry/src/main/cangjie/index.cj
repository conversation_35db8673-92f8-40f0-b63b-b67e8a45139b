package ohos_app_cangjie_entry

internal import ohos.base.LengthProp
internal import ohos.base.AppLog
internal import ohos.component.Column
internal import ohos.component.Row
internal import ohos.component.Button
internal import ohos.component.Text
internal import ohos.component.TextInput
internal import ohos.component.CustomView
internal import ohos.component.CJEntry
internal import ohos.component.loadNativeView
internal import ohos.component.FlexAlign
internal import ohos.component.HorizontalAlign
internal import ohos.component.TextAlign
internal import ohos.component.If
internal import ohos.component.ViewBuilder
internal import ohos.state_manage.SubscriberManager
internal import ohos.state_manage.ObservedProperty
internal import ohos.state_manage.LocalStorage
import ohos.state_macro_manage.Entry
import ohos.state_macro_manage.Component
import ohos.state_macro_manage.State
import ohos.state_macro_manage.r

/**
 * 应用主入口
 * 作业辅助APP的完整界面
 */
@Entry
@Component
class EntryView {
    @State
    var currentPage: String = "home"

    @State
    var userName: String = "小朋友"

    @State
    var todayTasks: Int64 = 5

    @State
    var completedTasks: Int64 = 3

    @State
    var bestDictationScore: Int64 = 92

    @State
    var bestRecitationScore: Int64 = 88

    // 听写相关状态
    @State
    var dictationWord: String = "苹果"

    @State
    var dictationInput: String = ""

    @State
    var dictationIndex: Int64 = 1

    @State
    var dictationTotal: Int64 = 5

    @State
    var dictationScore: Int64 = 0

    @State
    var showDictationResult: Bool = false

    // 背诵相关状态
    @State
    var recitationContent: String = "床前明月光，疑是地上霜。举头望明月，低头思故乡。"

    @State
    var recitationInput: String = ""

    @State
    var showRecitationResult: Bool = false

    @State
    var recitationScore: Int64 = 0

    @State
    var isRecording: Bool = false

    func build() {
        Column {
            if (this.currentPage == "home") {
                this.buildHomePage()
            } else if (this.currentPage == "dictation") {
                this.buildDictationPage()
            } else if (this.currentPage == "recitation") {
                this.buildRecitationPage()
            } else {
                this.buildHomePage()
            }
        }.width(100.percent).height(100.percent)
    }

    /**
     * 构建主页
     */
    private func buildHomePage() {
        Column {
            // 欢迎信息
            Text("欢迎回来！")
                .fontSize(28)
                .margin(top: 40, bottom: 16)

            Text("${this.userName}，今天也要加油学习哦！")
                .fontSize(16)
                .margin(bottom: 32)

            // 今日统计
            Row {
                Column {
                    Text("${this.todayTasks}")
                        .fontSize(24)
                    Text("今日任务")
                        .fontSize(14)
                        .margin(top: 4)
                }.layoutWeight(1)

                Column {
                    Text("${this.completedTasks}")
                        .fontSize(24)
                    Text("已完成")
                        .fontSize(14)
                        .margin(top: 4)
                }.layoutWeight(1)
            }.width(100.percent)
             .margin(bottom: 32)

            // 功能按钮
            Button("📝 智能听写")
                .fontSize(18)
                .width(280)
                .height(60)
                .margin(bottom: 16)
                .onClick { evt =>
                    AppLog.info("Navigate to dictation")
                    this.currentPage = "dictation"
                    this.resetDictation()
                }

            Button("📖 智能背诵")
                .fontSize(18)
                .width(280)
                .height(60)
                .margin(bottom: 32)
                .onClick { evt =>
                    AppLog.info("Navigate to recitation")
                    this.currentPage = "recitation"
                    this.resetRecitation()
                }

            // 最佳成绩
            Row {
                Column {
                    Text("听写最佳")
                        .fontSize(14)
                        .margin(bottom: 4)
                    Text("${this.bestDictationScore}分")
                        .fontSize(20)
                }.layoutWeight(1)

                Column {
                    Text("背诵最佳")
                        .fontSize(14)
                        .margin(bottom: 4)
                    Text("${this.bestRecitationScore}分")
                        .fontSize(20)
                }.layoutWeight(1)
            }.width(100.percent)

        }.width(100.percent).height(100.percent)
         .justifyContent(FlexAlign.Center)
         .alignItems(HorizontalAlign.Center)
    }

    /**
     * 构建听写页面
     */
    private func buildDictationPage() {
        Column {
            // 标题和返回按钮
            Row {
                Button("← 返回")
                    .fontSize(16)
                    .onClick { evt =>
                        this.currentPage = "home"
                    }

                Text("智能听写")
                    .fontSize(20)
                    .layoutWeight(1)
                    .textAlign(TextAlign.Center)

            }.width(100.percent)
             .height(56)
             .padding(left: 16, right: 16)

            if (this.showDictationResult) {
                // 结果页面
                Column {
                    Text("🎉")
                        .fontSize(64)
                        .margin(bottom: 16)

                    Text("听写完成！")
                        .fontSize(24)
                        .margin(bottom: 16)

                    Text("${this.dictationScore}分")
                        .fontSize(48)
                        .margin(bottom: 32)

                    Button("重新练习")
                        .fontSize(16)
                        .width(200)
                        .height(48)
                        .margin(bottom: 16)
                        .onClick { evt =>
                            this.resetDictation()
                        }

                    Button("返回首页")
                        .fontSize(16)
                        .width(200)
                        .height(48)
                        .onClick { evt =>
                            this.currentPage = "home"
                        }

                }.width(100.percent)
                 .alignItems(HorizontalAlign.Center)
                 .justifyContent(FlexAlign.Center)
                 .layoutWeight(1)
            } else {
                // 听写页面
                Column {
                    // 进度
                    Text("进度: ${this.dictationIndex}/${this.dictationTotal}")
                        .fontSize(16)
                        .margin(bottom: 24)

                    // 当前词语（隐藏显示）
                    Text("请听写词语")
                        .fontSize(18)
                        .margin(bottom: 16)

                    // 播放按钮
                    Button("🔊 播放语音")
                        .fontSize(18)
                        .width(200)
                        .height(80)
                        .margin(bottom: 32)
                        .onClick { evt =>
                            AppLog.info("Playing word: ${this.dictationWord}")
                        }

                    // 输入框
                    TextInput()
                        .fontSize(18)
                        .width(280)
                        .height(56)
                        .margin(bottom: 24)
                        .onChange { value =>
                            this.dictationInput = value
                        }

                    // 控制按钮
                    Row {
                        Button("跳过")
                            .fontSize(16)
                            .width(120)
                            .height(48)
                            .margin(right: 16)
                            .onClick { evt =>
                                this.nextDictationWord()
                            }

                        Button("提交")
                            .fontSize(16)
                            .width(120)
                            .height(48)
                            .onClick { evt =>
                                this.submitDictationAnswer()
                            }
                    }

                }.width(100.percent)
                 .alignItems(HorizontalAlign.Center)
                 .justifyContent(FlexAlign.Center)
                 .layoutWeight(1)
            }

        }.width(100.percent).height(100.percent)
    }

    /**
     * 构建背诵页面
     */
    private func buildRecitationPage() {
        Column {
            // 标题和返回按钮
            Row {
                Button("← 返回")
                    .fontSize(16)
                    .onClick { evt =>
                        this.currentPage = "home"
                    }

                Text("智能背诵")
                    .fontSize(20)
                    .layoutWeight(1)
                    .textAlign(TextAlign.Center)

            }.width(100.percent)
             .height(56)
             .padding(left: 16, right: 16)

            if (this.showRecitationResult) {
                // 结果页面
                Column {
                    Text("🎉")
                        .fontSize(64)
                        .margin(bottom: 16)

                    Text("背诵完成！")
                        .fontSize(24)
                        .margin(bottom: 16)

                    Text("${this.recitationScore}分")
                        .fontSize(48)
                        .margin(bottom: 32)

                    Button("重新背诵")
                        .fontSize(16)
                        .width(200)
                        .height(48)
                        .margin(bottom: 16)
                        .onClick { evt =>
                            this.resetRecitation()
                        }

                    Button("返回首页")
                        .fontSize(16)
                        .width(200)
                        .height(48)
                        .onClick { evt =>
                            this.currentPage = "home"
                        }

                }.width(100.percent)
                 .alignItems(HorizontalAlign.Center)
                 .justifyContent(FlexAlign.Center)
                 .layoutWeight(1)
            } else {
                // 背诵页面
                Column {
                    // 背诵内容
                    Text("背诵内容：")
                        .fontSize(16)
                        .margin(bottom: 8)

                    Text(this.recitationContent)
                        .fontSize(18)
                        .lineHeight(28)
                        .padding(16)
                        .margin(bottom: 24)
                        .width(300)
                        .textAlign(TextAlign.Center)

                    // 录音按钮
                    Button("🎤 开始背诵")
                        .fontSize(18)
                        .width(200)
                        .height(80)
                        .margin(bottom: 24)
                        .onClick { evt =>
                            this.toggleRecording()
                        }

                    // 文字输入（可选）
                    Text("或者输入背诵内容：")
                        .fontSize(14)
                        .margin(bottom: 8)

                    TextInput()
                        .fontSize(16)
                        .width(300)
                        .height(80)
                        .margin(bottom: 24)
                        .onChange { value =>
                            this.recitationInput = value
                        }

                    // 完成按钮
                    Button("完成背诵")
                        .fontSize(16)
                        .width(200)
                        .height(48)
                        .onClick { evt =>
                            this.finishRecitation()
                        }

                }.width(100.percent)
                 .alignItems(HorizontalAlign.Center)
                 .justifyContent(FlexAlign.Center)
                 .layoutWeight(1)
            }

        }.width(100.percent).height(100.percent)
    }

    // 听写相关方法
    private func resetDictation(): Unit {
        this.showDictationResult = false
        this.dictationIndex = 1
        this.dictationInput = ""
        this.dictationWord = "苹果"
        this.dictationScore = 0
    }

    private func nextDictationWord(): Unit {
        if (this.dictationIndex < this.dictationTotal) {
            this.dictationIndex += 1
            this.dictationInput = ""
            this.dictationWord = "词语${this.dictationIndex}"
        } else {
            this.finishDictation()
        }
    }

    private func submitDictationAnswer(): Unit {
        AppLog.info("Submit dictation answer: ${this.dictationInput}")
        // 简单评分逻辑
        if (this.dictationInput == this.dictationWord) {
            this.dictationScore += 20
        }
        this.nextDictationWord()
    }

    private func finishDictation(): Unit {
        this.showDictationResult = true
        if (this.dictationScore > this.bestDictationScore) {
            this.bestDictationScore = this.dictationScore
        }
    }

    // 背诵相关方法
    private func resetRecitation(): Unit {
        this.showRecitationResult = false
        this.recitationInput = ""
        this.recitationScore = 0
        this.isRecording = false
    }

    private func toggleRecording(): Unit {
        this.isRecording = !this.isRecording
        AppLog.info("Recording: ${this.isRecording}")
    }

    private func finishRecitation(): Unit {
        AppLog.info("Finish recitation")
        // 简单评分逻辑
        this.recitationScore = 85
        this.showRecitationResult = true
        if (this.recitationScore > this.bestRecitationScore) {
            this.bestRecitationScore = this.recitationScore
        }
    }
}
