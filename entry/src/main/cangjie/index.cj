package ohos_app_cangjie_entry

internal import ohos.base.LengthProp
internal import ohos.base.AppLog
internal import ohos.component.Column
internal import ohos.component.Row
internal import ohos.component.Button
internal import ohos.component.Text
internal import ohos.component.CustomView
internal import ohos.component.CJEntry
internal import ohos.component.loadNativeView
internal import ohos.state_manage.SubscriberManager
internal import ohos.state_manage.ObservedProperty
internal import ohos.state_manage.LocalStorage
import ohos.state_macro_manage.Entry
import ohos.state_macro_manage.Component
import ohos.state_macro_manage.State
import ohos.state_macro_manage.r

/**
 * 应用主入口
 * 作业辅助APP的主界面
 */
@Entry
@Component
class EntryView {
    @State
    var message: String = "作业辅助APP"

    func build() {
        Column {
            Text(this.message)
                .fontSize(24)
                .margin(top: 40, bottom: 40)

            Button("智能听写")
                .fontSize(18)
                .width(200)
                .height(60)
                .margin(bottom: 20)
                .onClick { evt =>
                    AppLog.info("Navigate to dictation")
                }

            Button("智能背诵")
                .fontSize(18)
                .width(200)
                .height(60)
                .onClick { evt =>
                    AppLog.info("Navigate to recitation")
                }

        }.width(100.percent).height(100.percent)
    }
}
