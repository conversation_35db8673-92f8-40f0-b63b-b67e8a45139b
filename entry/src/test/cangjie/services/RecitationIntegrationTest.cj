package ohos_app_cangjie_entry.services

import ohos_app_cangjie_entry.models.*

/**
 * 背诵服务集成测试类
 * 用于验证背诵服务与数据模型的集成
 */
public class RecitationIntegrationTest {
    
    /**
     * 测试完整的背诵流程
     * 验证从创建任务到完成评估的整个流程
     */
    public func testCompleteRecitationFlow(): Unit {
        println("Testing complete recitation flow...")
        
        // Arrange
        let service = RecitationService()
        let content = "春眠不觉晓，处处闻啼鸟。夜来风雨声，花落知多少。"
        
        // Act & Assert - 创建任务
        let task = service.createTask("春晓背诵测试", content, RecitationType.ChinesePoetry)
        if (task.title != "春晓背诵测试") {
            println("❌ Failed: Task creation")
            return
        }
        println("✅ Task created successfully")
        
        // Act & Assert - 开始背诵
        let startResult = service.startRecitation(task)
        if (!startResult.success) {
            println("❌ Failed: Start recitation - ${startResult.message}")
            return
        }
        println("✅ Recitation started successfully")
        
        // 验证任务状态
        let isInProgress = match (task.status) {
            case RecitationStatus.InProgress => true
            case _ => false
        }
        if (!isInProgress) {
            println("❌ Failed: Task should be in InProgress status")
            return
        }
        println("✅ Task status is InProgress")
        
        // Act & Assert - 提交第一次尝试
        let attempt1Result = service.submitRecitationAttempt(task, "/test/recitation.wav")
        if (!attempt1Result.success) {
            println("❌ Failed: Submit first attempt - ${attempt1Result.message}")
            return
        }
        println("✅ First attempt submitted, score: ${task.bestScore}")
        
        // Act & Assert - 提交第二次尝试（更好的表现）
        let attempt2Result = service.submitRecitationAttempt(task, "/test/excellent_recitation.wav")
        if (!attempt2Result.success) {
            println("❌ Failed: Submit second attempt - ${attempt2Result.message}")
            return
        }
        println("✅ Second attempt submitted, best score: ${task.bestScore}")
        
        // 验证尝试次数
        if (task.attempts.size != 2) {
            println("❌ Failed: Should have 2 attempts, got ${task.attempts.size}")
            return
        }
        println("✅ Attempt count is correct")
        
        // Act & Assert - 完成背诵
        let completeResult = service.completeRecitation(task)
        if (!completeResult.success) {
            println("❌ Failed: Complete recitation - ${completeResult.message}")
            return
        }
        println("✅ Recitation completed successfully")
        
        // 验证最终结果
        if (task.bestScore <= 0.0) {
            println("❌ Failed: Best score should be greater than 0")
            return
        }
        
        println("✅ Complete recitation flow test passed! Best score: ${task.bestScore}")
    }
    
    /**
     * 测试语音识别功能
     */
    public func testSpeechRecognitionFlow(): Unit {
        println("Testing speech recognition flow...")
        
        // Arrange
        let service = RecitationService()
        
        // Act & Assert - 正常音频识别
        let result1 = service.recognizeSpeech("/test/audio.wav")
        if (!result1.success) {
            println("❌ Failed: Speech recognition - ${result1.message}")
            return
        }
        if (result1.text == "") {
            println("❌ Failed: Recognized text should not be empty")
            return
        }
        println("✅ Speech recognition successful: '${result1.text}' (confidence: ${result1.confidence}%)")
        
        // Act & Assert - 实时语音识别
        let audioData = Array<String>(4, {i => "audio_chunk_${i}"})
        let result2 = service.recognizeRealTimeSpeech(audioData)
        if (!result2.success) {
            println("❌ Failed: Real-time speech recognition - ${result2.message}")
            return
        }
        println("✅ Real-time speech recognition successful: '${result2.text}'")
        
        // Act & Assert - 错误处理
        let result3 = service.recognizeSpeech("")
        if (result3.success) {
            println("❌ Failed: Should fail with empty audio path")
            return
        }
        println("✅ Empty audio path error handling works")
    }
    
    /**
     * 测试背诵评估功能
     */
    public func testRecitationEvaluation(): Unit {
        println("Testing recitation evaluation...")
        
        // Arrange
        let service = RecitationService()
        let originalText = "春眠不觉晓，处处闻啼鸟。"
        
        // Act & Assert - 完全正确的背诵
        let result1 = service.evaluateRecitation(originalText, originalText, 5000)
        if (!result1.success) {
            println("❌ Failed: Perfect recitation evaluation - ${result1.message}")
            return
        }
        if (result1.accuracyScore != 100.0) {
            println("❌ Failed: Perfect recitation should get 100% accuracy")
            return
        }
        println("✅ Perfect recitation evaluation: Accuracy=${result1.accuracyScore}, Fluency=${result1.fluencyScore}, Completeness=${result1.completenessScore}")
        
        // Act & Assert - 部分正确的背诵
        let partialText = "春眠不觉晓"
        let result2 = service.evaluateRecitation(originalText, partialText, 3000)
        if (!result2.success) {
            println("❌ Failed: Partial recitation evaluation - ${result2.message}")
            return
        }
        if (result2.completenessScore >= 100.0) {
            println("❌ Failed: Partial recitation should not get 100% completeness")
            return
        }
        println("✅ Partial recitation evaluation: Accuracy=${result2.accuracyScore}, Fluency=${result2.fluencyScore}, Completeness=${result2.completenessScore}")
        
        // Act & Assert - 错误处理
        let result3 = service.evaluateRecitation("", "test", 1000)
        if (result3.success) {
            println("❌ Failed: Should fail with empty original text")
            return
        }
        println("✅ Empty original text error handling works")
    }
    
    /**
     * 测试多次尝试的得分管理
     */
    public func testMultipleAttemptsScoring(): Unit {
        println("Testing multiple attempts scoring...")
        
        // Arrange
        let service = RecitationService()
        let content = "测试背诵内容"
        let task = service.createTask("多次尝试测试", content, RecitationType.ChineseText)
        service.startRecitation(task)
        
        // Act - 提交多次尝试
        service.submitRecitationAttempt(task, "/test/attempt1.wav")
        let firstScore = task.bestScore
        let firstAttemptCount = task.attempts.size
        
        service.submitRecitationAttempt(task, "/test/attempt2.wav")
        let secondScore = task.bestScore
        let secondAttemptCount = task.attempts.size
        
        // Assert
        if (firstAttemptCount != 1) {
            println("❌ Failed: Should have 1 attempt after first submission")
            return
        }
        
        if (secondAttemptCount != 2) {
            println("❌ Failed: Should have 2 attempts after second submission")
            return
        }
        
        if (secondScore < firstScore) {
            println("❌ Failed: Best score should not decrease")
            return
        }
        
        // 验证平均分计算
        let averageScore = task.getAverageScore()
        if (averageScore <= 0.0) {
            println("❌ Failed: Average score should be greater than 0")
            return
        }
        
        println("✅ Multiple attempts scoring works correctly")
        println("   First score: ${firstScore}, Second score: ${secondScore}")
        println("   Best score: ${task.bestScore}, Average score: ${averageScore}")
    }
    
    /**
     * 测试不同内容类型的背诵
     */
    public func testDifferentContentTypes(): Unit {
        println("Testing different content types...")
        
        // Arrange
        let service = RecitationService()
        
        // Act & Assert - 中文古诗
        let poetryTask = service.createTask("古诗测试", "床前明月光，疑是地上霜。", RecitationType.ChinesePoetry)
        if (poetryTask.contentType != RecitationType.ChinesePoetry) {
            println("❌ Failed: Poetry content type mismatch")
            return
        }
        println("✅ Chinese poetry task created")
        
        // Act & Assert - 中文课文
        let textTask = service.createTask("课文测试", "这是一段中文课文内容。", RecitationType.ChineseText)
        if (textTask.contentType != RecitationType.ChineseText) {
            println("❌ Failed: Text content type mismatch")
            return
        }
        println("✅ Chinese text task created")
        
        // Act & Assert - 英文课文
        let englishTask = service.createTask("English Test", "This is an English text.", RecitationType.EnglishText)
        if (englishTask.contentType != RecitationType.EnglishText) {
            println("❌ Failed: English text content type mismatch")
            return
        }
        println("✅ English text task created")
        
        // Act & Assert - 英文单词
        let wordsTask = service.createTask("Words Test", "apple banana orange", RecitationType.EnglishWords)
        if (wordsTask.contentType != RecitationType.EnglishWords) {
            println("❌ Failed: English words content type mismatch")
            return
        }
        println("✅ English words task created")
    }
    
    /**
     * 测试任务完成条件
     */
    public func testTaskCompletionConditions(): Unit {
        println("Testing task completion conditions...")
        
        // Arrange
        let service = RecitationService()
        let content = "测试内容"
        let task = service.createTask("完成条件测试", content, RecitationType.ChineseText)
        service.startRecitation(task)
        
        // Act & Assert - 没有尝试时不能完成
        let result1 = service.completeRecitation(task)
        if (result1.success && task.status == RecitationStatus.Completed) {
            println("❌ Failed: Should not complete without attempts")
            return
        }
        println("✅ Cannot complete without attempts")
        
        // Act - 提交一次尝试
        service.submitRecitationAttempt(task, "/test/recitation.wav")
        
        // Act & Assert - 有尝试后可以完成
        let result2 = service.completeRecitation(task)
        if (!result2.success) {
            println("❌ Failed: Should be able to complete with attempts - ${result2.message}")
            return
        }
        println("✅ Can complete with attempts")
        
        // 验证是否需要更多练习
        let needsMorePractice = task.needsMorePractice()
        println("   Needs more practice: ${needsMorePractice}")
        println("   Best score: ${task.bestScore}")
    }
    
    /**
     * 运行所有集成测试
     */
    public func runAllTests(): Unit {
        println("=== Running Recitation Service Integration Tests ===")
        testCompleteRecitationFlow()
        println("")
        testSpeechRecognitionFlow()
        println("")
        testRecitationEvaluation()
        println("")
        testMultipleAttemptsScoring()
        println("")
        testDifferentContentTypes()
        println("")
        testTaskCompletionConditions()
        println("=== Recitation Service Integration Tests Completed ===")
    }
}

/**
 * 主函数 - 用于运行测试
 */
public func main(): Unit {
    let test = RecitationIntegrationTest()
    test.runAllTests()
}
