package ohos_app_cangjie_entry.services

import ohos_app_cangjie_entry.models.*

/**
 * 听写服务测试类
 * 
 * 测试理由：
 * 1. DictationService是核心业务服务，包含复杂的AI集成逻辑
 * 2. 需要验证语音播报、OCR识别、批改等功能的正确性
 * 3. 需要验证错误处理和边界条件
 * 4. 需要验证与数据模型的集成
 */
public class DictationServiceTest {
    
    /**
     * 测试听写服务的基本创建
     * 验证服务初始化是否正确
     */
    public func testDictationServiceCreation(): Unit {
        // Arrange & Act
        let service = DictationService()
        
        // Assert
        if (!service.isInitialized()) {
            println("Test failed: service should be initialized")
        } else {
            println("Test passed: DictationService creation")
        }
    }
    
    /**
     * 测试创建听写任务
     * 验证任务创建功能是否正确
     */
    public func testCreateDictationTask(): Unit {
        // Arrange
        let service = DictationService()
        let words = Array<String>(3, {i => 
            match (i) {
                case 0 => "苹果"
                case 1 => "香蕉"
                case _ => "橘子"
            }
        })
        
        // Act
        let task = service.createTask("水果听写", words)
        
        // Assert
        if (task.title != "水果听写") {
            println("Test failed: expected '水果听写', got '${task.title}'")
        } else if (task.content.size != 3) {
            println("Test failed: expected 3 words, got ${task.content.size}")
        } else if (task.status != DictationStatus.NotStarted) {
            println("Test failed: task should be NotStarted")
        } else {
            println("Test passed: Create dictation task")
        }
    }
    
    /**
     * 测试开始听写任务
     * 验证任务启动和语音播报功能
     */
    public func testStartDictation(): Unit {
        // Arrange
        let service = DictationService()
        let words = Array<String>(2, {i => 
            match (i) {
                case 0 => "测试"
                case _ => "词语"
            }
        })
        let task = service.createTask("测试听写", words)
        
        // Act
        let result = service.startDictation(task)
        
        // Assert
        if (!result.success) {
            println("Test failed: start dictation should succeed, error: ${result.message}")
        } else if (task.status != DictationStatus.InProgress) {
            println("Test failed: task status should be InProgress")
        } else if (task.getCurrentContent() != "测试") {
            println("Test failed: current content should be '测试', got '${task.getCurrentContent()}'")
        } else {
            println("Test passed: Start dictation")
        }
    }
    
    /**
     * 测试语音播报功能
     * 验证TTS功能是否正常工作
     */
    public func testSpeakWord(): Unit {
        // Arrange
        let service = DictationService()
        let word = "苹果"
        
        // Act
        let result = service.speakWord(word)
        
        // Assert
        if (!result.success) {
            println("Test failed: speak word should succeed, error: ${result.message}")
        } else {
            println("Test passed: Speak word")
        }
    }
    
    /**
     * 测试OCR识别功能
     * 验证图片文字识别是否正确
     */
    public func testRecognizeFromImage(): Unit {
        // Arrange
        let service = DictationService()
        let imagePath = "/test/image.jpg"  // 模拟图片路径
        
        // Act
        let result = service.recognizeFromImage(imagePath)
        
        // Assert
        if (!result.success) {
            println("Test failed: OCR recognition should succeed, error: ${result.message}")
        } else if (result.text == "") {
            println("Test failed: recognized text should not be empty")
        } else {
            println("Test passed: OCR recognition, text: '${result.text}'")
        }
    }
    
    /**
     * 测试手写识别功能
     * 验证手写输入识别是否正确
     */
    public func testRecognizeHandwriting(): Unit {
        // Arrange
        let service = DictationService()
        let strokes = Array<String>(2, {i => 
            match (i) {
                case 0 => "stroke1"
                case _ => "stroke2"
            }
        })  // 模拟笔画数据
        
        // Act
        let result = service.recognizeHandwriting(strokes)
        
        // Assert
        if (!result.success) {
            println("Test failed: handwriting recognition should succeed, error: ${result.message}")
        } else if (result.text == "") {
            println("Test failed: recognized text should not be empty")
        } else {
            println("Test passed: Handwriting recognition, text: '${result.text}'")
        }
    }
    
    /**
     * 测试提交答案功能
     * 验证答案提交和自动批改
     */
    public func testSubmitAnswer(): Unit {
        // Arrange
        let service = DictationService()
        let words = Array<String>(2, {i => 
            match (i) {
                case 0 => "苹果"
                case _ => "香蕉"
            }
        })
        let task = service.createTask("测试听写", words)
        service.startDictation(task)
        
        // Act
        let result = service.submitAnswer(task, "苹果")
        
        // Assert
        if (!result.success) {
            println("Test failed: submit answer should succeed, error: ${result.message}")
        } else if (task.currentIndex != 1) {
            println("Test failed: current index should be 1, got ${task.currentIndex}")
        } else if (task.userAnswers.size != 1) {
            println("Test failed: should have 1 answer, got ${task.userAnswers.size}")
        } else {
            println("Test passed: Submit answer")
        }
    }
    
    /**
     * 测试完成听写任务
     * 验证任务完成和批改功能
     */
    public func testCompleteDictation(): Unit {
        // Arrange
        let service = DictationService()
        let words = Array<String>(2, {i => 
            match (i) {
                case 0 => "苹果"
                case _ => "香蕉"
            }
        })
        let task = service.createTask("测试听写", words)
        service.startDictation(task)
        service.submitAnswer(task, "苹果")
        service.submitAnswer(task, "香蕉")
        
        // Act
        let result = service.completeDictation(task)
        
        // Assert
        if (!result.success) {
            println("Test failed: complete dictation should succeed, error: ${result.message}")
        } else if (task.status != DictationStatus.Completed) {
            println("Test failed: task status should be Completed")
        } else if (task.score <= 0.0) {
            println("Test failed: score should be greater than 0, got ${task.score}")
        } else {
            println("Test passed: Complete dictation, score: ${task.score}")
        }
    }
    
    /**
     * 测试错误处理
     * 验证各种错误情况的处理
     */
    public func testErrorHandling(): Unit {
        // Arrange
        let service = DictationService()
        let words = Array<String>(1, {i => "测试"})
        let task = service.createTask("测试听写", words)
        
        // Act & Assert - 未开始任务就提交答案
        let result1 = service.submitAnswer(task, "答案")
        if (result1.success) {
            println("Test failed: should not allow submit answer before start")
        } else {
            println("Test passed: Error handling - submit before start")
        }
        
        // Act & Assert - 空图片路径OCR
        let result2 = service.recognizeFromImage("")
        if (result2.success) {
            println("Test failed: should not allow empty image path")
        } else {
            println("Test passed: Error handling - empty image path")
        }
    }
    
    /**
     * 运行所有听写服务测试
     */
    public func runAllTests(): Unit {
        println("Running dictation service tests...")
        testDictationServiceCreation()
        testCreateDictationTask()
        testStartDictation()
        testSpeakWord()
        testRecognizeFromImage()
        testRecognizeHandwriting()
        testSubmitAnswer()
        testCompleteDictation()
        testErrorHandling()
        println("Dictation service tests completed.")
    }
}

/**
 * 主函数 - 用于运行测试
 */
public func main(): Unit {
    let test = DictationServiceTest()
    test.runAllTests()
}
