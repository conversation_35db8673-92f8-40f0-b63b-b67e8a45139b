package ohos_app_cangjie_entry.services

import ohos_app_cangjie_entry.models.*

/**
 * 背诵服务测试类
 * 
 * 测试理由：
 * 1. RecitationService是核心业务服务，包含复杂的语音评测逻辑
 * 2. 需要验证语音识别、流利度分析、准确性评估等功能的正确性
 * 3. 需要验证错误处理和边界条件
 * 4. 需要验证与数据模型的集成
 */
public class RecitationServiceTest {
    
    /**
     * 测试背诵服务的基本创建
     * 验证服务初始化是否正确
     */
    public func testRecitationServiceCreation(): Unit {
        // Arrange & Act
        let service = RecitationService()
        
        // Assert
        if (!service.isInitialized()) {
            println("Test failed: service should be initialized")
        } else {
            println("Test passed: RecitationService creation")
        }
    }
    
    /**
     * 测试创建背诵任务
     * 验证任务创建功能是否正确
     */
    public func testCreateRecitationTask(): Unit {
        // Arrange
        let service = RecitationService()
        let content = "春眠不觉晓，处处闻啼鸟。夜来风雨声，花落知多少。"
        
        // Act
        let task = service.createTask("春晓背诵", content, RecitationType.ChinesePoetry)
        
        // Assert
        if (task.title != "春晓背诵") {
            println("Test failed: expected '春晓背诵', got '${task.title}'")
        } else if (task.content != content) {
            println("Test failed: content mismatch")
        } else if (task.contentType != RecitationType.ChinesePoetry) {
            println("Test failed: content type mismatch")
        } else if (task.status != RecitationStatus.NotStarted) {
            println("Test failed: task should be NotStarted")
        } else {
            println("Test passed: Create recitation task")
        }
    }
    
    /**
     * 测试开始背诵任务
     * 验证任务启动功能
     */
    public func testStartRecitation(): Unit {
        // Arrange
        let service = RecitationService()
        let content = "测试背诵内容"
        let task = service.createTask("测试背诵", content, RecitationType.ChineseText)
        
        // Act
        let result = service.startRecitation(task)
        
        // Assert
        if (!result.success) {
            println("Test failed: start recitation should succeed, error: ${result.message}")
        } else if (task.status != RecitationStatus.InProgress) {
            println("Test failed: task status should be InProgress")
        } else {
            println("Test passed: Start recitation")
        }
    }
    
    /**
     * 测试语音识别功能
     * 验证ASR功能是否正常工作
     */
    public func testSpeechRecognition(): Unit {
        // Arrange
        let service = RecitationService()
        let audioPath = "/test/audio.wav"  // 模拟音频路径
        
        // Act
        let result = service.recognizeSpeech(audioPath)
        
        // Assert
        if (!result.success) {
            println("Test failed: speech recognition should succeed, error: ${result.message}")
        } else if (result.text == "") {
            println("Test failed: recognized text should not be empty")
        } else {
            println("Test passed: Speech recognition, text: '${result.text}'")
        }
    }
    
    /**
     * 测试实时语音识别功能
     * 验证实时ASR功能
     */
    public func testRealTimeSpeechRecognition(): Unit {
        // Arrange
        let service = RecitationService()
        let audioData = Array<String>(3, {i => "audio_chunk_${i}"})  // 模拟音频数据块
        
        // Act
        let result = service.recognizeRealTimeSpeech(audioData)
        
        // Assert
        if (!result.success) {
            println("Test failed: real-time speech recognition should succeed, error: ${result.message}")
        } else if (result.text == "") {
            println("Test failed: recognized text should not be empty")
        } else {
            println("Test passed: Real-time speech recognition, text: '${result.text}'")
        }
    }
    
    /**
     * 测试背诵评估功能
     * 验证准确性、流利度、完整性评估
     */
    public func testEvaluateRecitation(): Unit {
        // Arrange
        let service = RecitationService()
        let originalText = "春眠不觉晓，处处闻啼鸟。"
        let recognizedText = "春眠不觉晓，处处闻啼鸟。"  // 完全正确
        let duration = 5000  // 5秒
        
        // Act
        let result = service.evaluateRecitation(originalText, recognizedText, duration)
        
        // Assert
        if (!result.success) {
            println("Test failed: evaluation should succeed, error: ${result.message}")
        } else if (result.accuracyScore <= 0.0) {
            println("Test failed: accuracy score should be greater than 0")
        } else if (result.fluencyScore <= 0.0) {
            println("Test failed: fluency score should be greater than 0")
        } else if (result.completenessScore <= 0.0) {
            println("Test failed: completeness score should be greater than 0")
        } else {
            println("Test passed: Evaluate recitation - Accuracy: ${result.accuracyScore}, Fluency: ${result.fluencyScore}, Completeness: ${result.completenessScore}")
        }
    }
    
    /**
     * 测试提交背诵尝试
     * 验证背诵尝试记录功能
     */
    public func testSubmitRecitationAttempt(): Unit {
        // Arrange
        let service = RecitationService()
        let content = "测试背诵内容"
        let task = service.createTask("测试背诵", content, RecitationType.ChineseText)
        service.startRecitation(task)
        
        let audioPath = "/test/recitation.wav"
        
        // Act
        let result = service.submitRecitationAttempt(task, audioPath)
        
        // Assert
        if (!result.success) {
            println("Test failed: submit attempt should succeed, error: ${result.message}")
        } else if (task.attempts.size != 1) {
            println("Test failed: should have 1 attempt, got ${task.attempts.size}")
        } else if (task.bestScore <= 0.0) {
            println("Test failed: best score should be greater than 0")
        } else {
            println("Test passed: Submit recitation attempt, score: ${task.bestScore}")
        }
    }
    
    /**
     * 测试完成背诵任务
     * 验证任务完成条件判断
     */
    public func testCompleteRecitation(): Unit {
        // Arrange
        let service = RecitationService()
        let content = "测试背诵内容"
        let task = service.createTask("测试背诵", content, RecitationType.ChineseText)
        service.startRecitation(task)
        
        // 提交一个高分尝试
        service.submitRecitationAttempt(task, "/test/excellent_recitation.wav")
        
        // Act
        let result = service.completeRecitation(task)
        
        // Assert
        if (!result.success) {
            println("Test failed: complete recitation should succeed, error: ${result.message}")
        } else {
            let isCompleted = match (task.status) {
                case RecitationStatus.Completed => true
                case _ => false
            }
            if (!isCompleted) {
                println("Test failed: task should be completed")
            } else {
                println("Test passed: Complete recitation")
            }
        }
    }
    
    /**
     * 测试错误处理
     * 验证各种错误情况的处理
     */
    public func testErrorHandling(): Unit {
        // Arrange
        let service = RecitationService()
        let content = "测试内容"
        let task = service.createTask("测试", content, RecitationType.ChineseText)
        
        // Act & Assert - 未开始任务就提交尝试
        let result1 = service.submitRecitationAttempt(task, "/test/audio.wav")
        if (result1.success) {
            println("Test failed: should not allow submit attempt before start")
        } else {
            println("Test passed: Error handling - submit before start")
        }
        
        // Act & Assert - 空音频路径
        let result2 = service.recognizeSpeech("")
        if (result2.success) {
            println("Test failed: should not allow empty audio path")
        } else {
            println("Test passed: Error handling - empty audio path")
        }
        
        // Act & Assert - 空文本评估
        let result3 = service.evaluateRecitation("", "test", 1000)
        if (result3.success) {
            println("Test failed: should not allow empty original text")
        } else {
            println("Test passed: Error handling - empty original text")
        }
    }
    
    /**
     * 测试多次尝试的最佳得分更新
     */
    public func testMultipleAttempts(): Unit {
        // Arrange
        let service = RecitationService()
        let content = "测试背诵内容"
        let task = service.createTask("多次尝试测试", content, RecitationType.ChineseText)
        service.startRecitation(task)
        
        // Act - 提交多次尝试
        service.submitRecitationAttempt(task, "/test/attempt1.wav")  // 第一次尝试
        let firstScore = task.bestScore
        
        service.submitRecitationAttempt(task, "/test/attempt2.wav")  // 第二次尝试
        let secondScore = task.bestScore
        
        // Assert
        if (task.attempts.size != 2) {
            println("Test failed: should have 2 attempts, got ${task.attempts.size}")
        } else if (secondScore < firstScore) {
            println("Test failed: best score should not decrease")
        } else {
            println("Test passed: Multiple attempts, best score: ${task.bestScore}")
        }
    }
    
    /**
     * 运行所有背诵服务测试
     */
    public func runAllTests(): Unit {
        println("Running recitation service tests...")
        testRecitationServiceCreation()
        testCreateRecitationTask()
        testStartRecitation()
        testSpeechRecognition()
        testRealTimeSpeechRecognition()
        testEvaluateRecitation()
        testSubmitRecitationAttempt()
        testCompleteRecitation()
        testErrorHandling()
        testMultipleAttempts()
        println("Recitation service tests completed.")
    }
}

/**
 * 主函数 - 用于运行测试
 */
public func main(): Unit {
    let test = RecitationServiceTest()
    test.runAllTests()
}
