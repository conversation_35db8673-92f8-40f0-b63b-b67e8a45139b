package ohos_app_cangjie_entry.services

import ohos_app_cangjie_entry.models.*

/**
 * 服务集成测试类
 * 用于验证听写服务与数据模型的集成
 */
public class ServiceIntegrationTest {
    
    /**
     * 测试完整的听写流程
     * 验证从创建任务到完成批改的整个流程
     */
    public func testCompleteDictationFlow(): Unit {
        println("Testing complete dictation flow...")
        
        // Arrange
        let service = DictationService()
        let words = Array<String>(3, {i => 
            match (i) {
                case 0 => "苹果"
                case 1 => "香蕉"
                case _ => "橘子"
            }
        })
        
        // Act & Assert - 创建任务
        let task = service.createTask("水果听写测试", words)
        if (task.title != "水果听写测试") {
            println("❌ Failed: Task creation")
            return
        }
        println("✅ Task created successfully")
        
        // Act & Assert - 开始听写
        let startResult = service.startDictation(task)
        if (!startResult.success) {
            println("❌ Failed: Start dictation - ${startResult.message}")
            return
        }
        println("✅ Dictation started successfully")
        
        // Act & Assert - 提交答案
        let answer1Result = service.submitAnswer(task, "苹果")
        if (!answer1Result.success) {
            println("❌ Failed: Submit first answer - ${answer1Result.message}")
            return
        }
        println("✅ First answer submitted")
        
        let answer2Result = service.submitAnswer(task, "香蕉")
        if (!answer2Result.success) {
            println("❌ Failed: Submit second answer - ${answer2Result.message}")
            return
        }
        println("✅ Second answer submitted")
        
        let answer3Result = service.submitAnswer(task, "橘子")
        if (!answer3Result.success) {
            println("❌ Failed: Submit third answer - ${answer3Result.message}")
            return
        }
        println("✅ Third answer submitted")
        
        // 验证任务状态
        let isPendingReview = match (task.status) {
            case DictationStatus.PendingReview => true
            case _ => false
        }
        if (!isPendingReview) {
            println("❌ Failed: Task should be in PendingReview status")
            return
        }
        println("✅ Task status is PendingReview")
        
        // Act & Assert - 完成听写
        let completeResult = service.completeDictation(task)
        if (!completeResult.success) {
            println("❌ Failed: Complete dictation - ${completeResult.message}")
            return
        }
        println("✅ Dictation completed successfully")
        
        // 验证最终结果
        let isCompleted = match (task.status) {
            case DictationStatus.Completed => true
            case _ => false
        }
        if (!isCompleted) {
            println("❌ Failed: Task should be completed")
            return
        }
        
        if (task.score != 100.0) {
            println("❌ Failed: Expected score 100.0, got ${task.score}")
            return
        }
        
        println("✅ Complete dictation flow test passed! Final score: ${task.score}")
    }
    
    /**
     * 测试OCR识别功能
     */
    public func testOCRRecognition(): Unit {
        println("Testing OCR recognition...")
        
        // Arrange
        let service = DictationService()
        
        // Act & Assert - 正常图片识别
        let result1 = service.recognizeFromImage("/test/apple.jpg")
        if (!result1.success) {
            println("❌ Failed: OCR recognition - ${result1.message}")
            return
        }
        if (result1.text != "苹果") {
            println("❌ Failed: Expected '苹果', got '${result1.text}'")
            return
        }
        println("✅ OCR recognition successful: '${result1.text}' (confidence: ${result1.confidence}%)")
        
        // Act & Assert - 空路径错误处理
        let result2 = service.recognizeFromImage("")
        if (result2.success) {
            println("❌ Failed: Should fail with empty path")
            return
        }
        println("✅ Empty path error handling works")
    }
    
    /**
     * 测试手写识别功能
     */
    public func testHandwritingRecognition(): Unit {
        println("Testing handwriting recognition...")
        
        // Arrange
        let service = DictationService()
        let strokes = Array<String>(2, {i => "stroke${i}"})
        
        // Act & Assert - 正常手写识别
        let result1 = service.recognizeHandwriting(strokes)
        if (!result1.success) {
            println("❌ Failed: Handwriting recognition - ${result1.message}")
            return
        }
        if (result1.text != "二") {
            println("❌ Failed: Expected '二', got '${result1.text}'")
            return
        }
        println("✅ Handwriting recognition successful: '${result1.text}' (confidence: ${result1.confidence}%)")
        
        // Act & Assert - 空笔画错误处理
        let emptyStrokes = Array<String>(0, {i => ""})
        let result2 = service.recognizeHandwriting(emptyStrokes)
        if (result2.success) {
            println("❌ Failed: Should fail with empty strokes")
            return
        }
        println("✅ Empty strokes error handling works")
    }
    
    /**
     * 测试语音播报功能
     */
    public func testSpeechFunction(): Unit {
        println("Testing speech function...")
        
        // Arrange
        let service = DictationService()
        
        // Act & Assert - 正常语音播报
        let result1 = service.speakWord("测试")
        if (!result1.success) {
            println("❌ Failed: Speech function - ${result1.message}")
            return
        }
        if (result1.duration <= 0) {
            println("❌ Failed: Duration should be greater than 0, got ${result1.duration}")
            return
        }
        println("✅ Speech function successful: duration ${result1.duration}ms")
        
        // Act & Assert - 空词语错误处理
        let result2 = service.speakWord("")
        if (result2.success) {
            println("❌ Failed: Should fail with empty word")
            return
        }
        println("✅ Empty word error handling works")
    }
    
    /**
     * 测试错误答案的批改
     */
    public func testIncorrectAnswers(): Unit {
        println("Testing incorrect answers correction...")
        
        // Arrange
        let service = DictationService()
        let words = Array<String>(2, {i => 
            match (i) {
                case 0 => "苹果"
                case _ => "香蕉"
            }
        })
        
        let task = service.createTask("错误答案测试", words)
        service.startDictation(task)
        
        // Act - 提交错误答案
        service.submitAnswer(task, "苹果")  // 正确
        service.submitAnswer(task, "桔子")  // 错误，应该是"香蕉"
        
        let completeResult = service.completeDictation(task)
        
        // Assert
        if (!completeResult.success) {
            println("❌ Failed: Complete dictation with errors - ${completeResult.message}")
            return
        }
        
        if (task.score != 50.0) {  // 2个中对1个，应该是50分
            println("❌ Failed: Expected score 50.0, got ${task.score}")
            return
        }
        
        if (task.corrections.size != 2) {
            println("❌ Failed: Expected 2 corrections, got ${task.corrections.size}")
            return
        }
        
        println("✅ Incorrect answers test passed! Score: ${task.score}")
    }
    
    /**
     * 运行所有集成测试
     */
    public func runAllTests(): Unit {
        println("=== Running Service Integration Tests ===")
        testCompleteDictationFlow()
        println("")
        testOCRRecognition()
        println("")
        testHandwritingRecognition()
        println("")
        testSpeechFunction()
        println("")
        testIncorrectAnswers()
        println("=== Service Integration Tests Completed ===")
    }
}

/**
 * 主函数 - 用于运行测试
 */
public func main(): Unit {
    let test = ServiceIntegrationTest()
    test.runAllTests()
}
