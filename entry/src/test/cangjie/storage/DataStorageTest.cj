package ohos_app_cangjie_entry.storage

import ohos_app_cangjie_entry.models.*

/**
 * 数据存储测试类
 * 
 * 测试理由：
 * 1. DataStorage是核心数据持久化服务，包含复杂的数据管理逻辑
 * 2. 需要验证用户配置、历史记录、错题集等数据的正确存储和读取
 * 3. 需要验证数据的序列化和反序列化
 * 4. 需要验证错误处理和边界条件
 */
public class DataStorageTest {
    
    /**
     * 测试数据存储服务的基本创建
     * 验证服务初始化是否正确
     */
    public func testDataStorageCreation(): Unit {
        // Arrange & Act
        let storage = DataStorage()
        
        // Assert
        if (!storage.isInitialized()) {
            println("Test failed: storage should be initialized")
        } else {
            println("Test passed: DataStorage creation")
        }
    }
    
    /**
     * 测试用户配置的保存和读取
     * 验证用户偏好设置的持久化
     */
    public func testSaveAndLoadUserPreferences(): Unit {
        // Arrange
        let storage = DataStorage()
        let preferences = UserPreferences()
        preferences.speechSpeed = 1.5
        preferences.speechVolume = 0.9
        preferences.voiceType = "female"
        preferences.enableSoundEffects = false
        
        // Act
        let saveResult = storage.saveUserPreferences("user001", preferences)
        
        // Assert
        if (!saveResult.success) {
            println("Test failed: save preferences should succeed, error: ${saveResult.message}")
            return
        }
        
        // Act - 读取配置
        let loadResult = storage.loadUserPreferences("user001")
        
        // Assert
        if (!loadResult.success) {
            println("Test failed: load preferences should succeed, error: ${loadResult.message}")
        } else if (loadResult.preferences.speechSpeed != 1.5) {
            println("Test failed: expected speech speed 1.5, got ${loadResult.preferences.speechSpeed}")
        } else if (loadResult.preferences.voiceType != "female") {
            println("Test failed: expected voice type 'female', got '${loadResult.preferences.voiceType}'")
        } else {
            println("Test passed: Save and load user preferences")
        }
    }
    
    /**
     * 测试用户档案的保存和读取
     * 验证用户档案数据的持久化
     */
    public func testSaveAndLoadUserProfile(): Unit {
        // Arrange
        let storage = DataStorage()
        let profile = UserProfile("user001", "小明", "三年级")
        profile.updateDictationStats(85.0, true)
        profile.updateRecitationStats(90.0, true)
        
        // Act
        let saveResult = storage.saveUserProfile(profile)
        
        // Assert
        if (!saveResult.success) {
            println("Test failed: save profile should succeed, error: ${saveResult.message}")
            return
        }
        
        // Act - 读取档案
        let loadResult = storage.loadUserProfile("user001")
        
        // Assert
        if (!loadResult.success) {
            println("Test failed: load profile should succeed, error: ${loadResult.message}")
        } else if (loadResult.profile.userName != "小明") {
            println("Test failed: expected user name '小明', got '${loadResult.profile.userName}'")
        } else if (loadResult.profile.bestDictationScore != 85.0) {
            println("Test failed: expected dictation score 85.0, got ${loadResult.profile.bestDictationScore}")
        } else {
            println("Test passed: Save and load user profile")
        }
    }
    
    /**
     * 测试听写任务的保存和读取
     * 验证听写任务数据的持久化
     */
    public func testSaveAndLoadDictationTask(): Unit {
        // Arrange
        let storage = DataStorage()
        let words = Array<String>(3, {i => 
            match (i) {
                case 0 => "苹果"
                case 1 => "香蕉"
                case _ => "橘子"
            }
        })
        let task = DictationTask("task001", "水果听写", words)
        task.start()
        task.submitAnswer("苹果")
        task.submitAnswer("香蕉")
        task.submitAnswer("橘子")
        
        // Act
        let saveResult = storage.saveDictationTask(task)
        
        // Assert
        if (!saveResult.success) {
            println("Test failed: save task should succeed, error: ${saveResult.message}")
            return
        }
        
        // Act - 读取任务
        let loadResult = storage.loadDictationTask("task001")
        
        // Assert
        if (!loadResult.success) {
            println("Test failed: load task should succeed, error: ${loadResult.message}")
        } else if (loadResult.task.title != "水果听写") {
            println("Test failed: expected title '水果听写', got '${loadResult.task.title}'")
        } else if (loadResult.task.userAnswers.size != 3) {
            println("Test failed: expected 3 answers, got ${loadResult.task.userAnswers.size}")
        } else {
            println("Test passed: Save and load dictation task")
        }
    }
    
    /**
     * 测试背诵任务的保存和读取
     * 验证背诵任务数据的持久化
     */
    public func testSaveAndLoadRecitationTask(): Unit {
        // Arrange
        let storage = DataStorage()
        let content = "春眠不觉晓，处处闻啼鸟。"
        let task = RecitationTask("task002", "春晓背诵", content, RecitationType.ChinesePoetry)
        task.start()
        
        // 添加一次尝试
        let errors = Array<RecitationError>(0, {i => 
            RecitationError(RecitationErrorType.MissingWord, 0, "", "", "")
        })
        let attempt = RecitationAttempt(
            "attempt001", "task002", "2025-07-02T10:00:00", "2025-07-02T10:01:00", 60,
            90.0, 85.0, 95.0, content, errors, "表现优秀"
        )
        task.addAttempt(attempt)
        
        // Act
        let saveResult = storage.saveRecitationTask(task)
        
        // Assert
        if (!saveResult.success) {
            println("Test failed: save recitation task should succeed, error: ${saveResult.message}")
            return
        }
        
        // Act - 读取任务
        let loadResult = storage.loadRecitationTask("task002")
        
        // Assert
        if (!loadResult.success) {
            println("Test failed: load recitation task should succeed, error: ${loadResult.message}")
        } else if (loadResult.task.title != "春晓背诵") {
            println("Test failed: expected title '春晓背诵', got '${loadResult.task.title}'")
        } else if (loadResult.task.attempts.size != 1) {
            println("Test failed: expected 1 attempt, got ${loadResult.task.attempts.size}")
        } else {
            println("Test passed: Save and load recitation task")
        }
    }
    
    /**
     * 测试获取用户的所有任务
     * 验证任务列表查询功能
     */
    public func testGetUserTasks(): Unit {
        // Arrange
        let storage = DataStorage()
        let userId = "user001"
        
        // 创建并保存一些任务
        let words = Array<String>(2, {i => 
            match (i) {
                case 0 => "测试"
                case _ => "词语"
            }
        })
        let dictationTask = DictationTask("dict001", "测试听写", words)
        let recitationTask = RecitationTask("reci001", "测试背诵", "测试内容", RecitationType.ChineseText)
        
        storage.saveDictationTask(dictationTask)
        storage.saveRecitationTask(recitationTask)
        
        // Act
        let result = storage.getUserTasks(userId)
        
        // Assert
        if (!result.success) {
            println("Test failed: get user tasks should succeed, error: ${result.message}")
        } else if (result.tasks.size < 2) {
            println("Test failed: expected at least 2 tasks, got ${result.tasks.size}")
        } else {
            println("Test passed: Get user tasks, found ${result.tasks.size} tasks")
        }
    }
    
    /**
     * 测试删除任务
     * 验证任务删除功能
     */
    public func testDeleteTask(): Unit {
        // Arrange
        let storage = DataStorage()
        let words = Array<String>(1, {i => "测试"})
        let task = DictationTask("delete_test", "删除测试", words)
        storage.saveDictationTask(task)
        
        // 验证任务存在
        let loadResult1 = storage.loadDictationTask("delete_test")
        if (!loadResult1.success) {
            println("Test failed: task should exist before deletion")
            return
        }
        
        // Act - 删除任务
        let deleteResult = storage.deleteDictationTask("delete_test")
        
        // Assert
        if (!deleteResult.success) {
            println("Test failed: delete task should succeed, error: ${deleteResult.message}")
            return
        }
        
        // 验证任务已删除
        let loadResult2 = storage.loadDictationTask("delete_test")
        if (loadResult2.success) {
            println("Test failed: task should not exist after deletion")
        } else {
            println("Test passed: Delete task")
        }
    }
    
    /**
     * 测试错误处理
     * 验证各种错误情况的处理
     */
    public func testErrorHandling(): Unit {
        // Arrange
        let storage = DataStorage()
        
        // Act & Assert - 读取不存在的用户配置
        let result1 = storage.loadUserPreferences("nonexistent_user")
        if (result1.success) {
            println("Test failed: should fail when loading nonexistent user preferences")
        } else {
            println("Test passed: Error handling - nonexistent user preferences")
        }
        
        // Act & Assert - 读取不存在的任务
        let result2 = storage.loadDictationTask("nonexistent_task")
        if (result2.success) {
            println("Test failed: should fail when loading nonexistent task")
        } else {
            println("Test passed: Error handling - nonexistent task")
        }
        
        // Act & Assert - 保存空ID的任务
        let words = Array<String>(1, {i => "测试"})
        let invalidTask = DictationTask("", "无效任务", words)
        let result3 = storage.saveDictationTask(invalidTask)
        if (result3.success) {
            println("Test failed: should fail when saving task with empty ID")
        } else {
            println("Test passed: Error handling - empty task ID")
        }
    }
    
    /**
     * 运行所有数据存储测试
     */
    public func runAllTests(): Unit {
        println("Running data storage tests...")
        testDataStorageCreation()
        testSaveAndLoadUserPreferences()
        testSaveAndLoadUserProfile()
        testSaveAndLoadDictationTask()
        testSaveAndLoadRecitationTask()
        testGetUserTasks()
        testDeleteTask()
        testErrorHandling()
        println("Data storage tests completed.")
    }
}

/**
 * 主函数 - 用于运行测试
 */
public func main(): Unit {
    let test = DataStorageTest()
    test.runAllTests()
}
