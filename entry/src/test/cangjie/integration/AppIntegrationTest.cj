package ohos_app_cangjie_entry.integration

import ohos_app_cangjie_entry.models.*
import ohos_app_cangjie_entry.services.*
import ohos_app_cangjie_entry.storage.*

/**
 * 应用集成测试类
 * 
 * 测试理由：
 * 1. 验证各个模块之间的协作是否正确
 * 2. 验证完整的用户使用流程
 * 3. 验证数据在各个服务之间的传递
 * 4. 验证错误处理和异常情况
 * 5. 验证性能和稳定性
 */
public class AppIntegrationTest {
    
    private var dictationService: DictationService
    private var recitationService: RecitationService
    private var dataStorage: DataStorage
    
    public init() {
        this.dictationService = DictationService()
        this.recitationService = RecitationService()
        this.dataStorage = DataStorage()
    }
    
    /**
     * 测试完整的听写流程
     * 验证从创建任务到保存结果的完整流程
     */
    public func testCompleteeDictationWorkflow(): Unit {
        println("Testing complete dictation workflow...")
        
        // Arrange - 准备测试数据
        let userId = "integration_user_001"
        let words = Array<String>(3, {i => 
            match (i) {
                case 0 => "集成"
                case 1 => "测试"
                case _ => "流程"
            }
        })
        
        // Act & Assert - 步骤1：创建听写任务
        let createResult = this.dictationService.createTask("integration_dict_001", "集成测试听写", words)
        if (!createResult.success) {
            println("Test failed: create dictation task should succeed, error: ${createResult.message}")
            return
        }
        
        // Act & Assert - 步骤2：开始任务
        let startResult = this.dictationService.startTask("integration_dict_001")
        if (!startResult.success) {
            println("Test failed: start dictation task should succeed, error: ${startResult.message}")
            return
        }
        
        // Act & Assert - 步骤3：提交答案
        let submitResult1 = this.dictationService.submitAnswer("integration_dict_001", "集成")
        let submitResult2 = this.dictationService.submitAnswer("integration_dict_001", "测试")
        let submitResult3 = this.dictationService.submitAnswer("integration_dict_001", "流程")
        
        if (!submitResult1.success || !submitResult2.success || !submitResult3.success) {
            println("Test failed: submit answers should succeed")
            return
        }
        
        // Act & Assert - 步骤4：完成任务
        let finishResult = this.dictationService.finishTask("integration_dict_001")
        if (!finishResult.success) {
            println("Test failed: finish dictation task should succeed, error: ${finishResult.message}")
            return
        }
        
        // Act & Assert - 步骤5：获取任务结果
        let taskResult = this.dictationService.getTask("integration_dict_001")
        if (!taskResult.success) {
            println("Test failed: get dictation task should succeed, error: ${taskResult.message}")
            return
        }
        
        // Act & Assert - 步骤6：保存到存储
        let saveResult = this.dataStorage.saveDictationTask(taskResult.task)
        if (!saveResult.success) {
            println("Test failed: save dictation task should succeed, error: ${saveResult.message}")
            return
        }
        
        // Act & Assert - 步骤7：从存储加载验证
        let loadResult = this.dataStorage.loadDictationTask("integration_dict_001")
        if (!loadResult.success) {
            println("Test failed: load dictation task should succeed, error: ${loadResult.message}")
            return
        }
        
        // 验证任务状态和结果
        if (loadResult.task.status != TaskStatus.Completed) {
            println("Test failed: task should be completed")
            return
        }
        
        if (loadResult.task.userAnswers.size != 3) {
            println("Test failed: should have 3 answers, got ${loadResult.task.userAnswers.size}")
            return
        }
        
        println("Test passed: Complete dictation workflow")
    }
    
    /**
     * 测试完整的背诵流程
     * 验证从创建任务到保存结果的完整流程
     */
    public func testCompleteRecitationWorkflow(): Unit {
        println("Testing complete recitation workflow...")
        
        // Arrange - 准备测试数据
        let userId = "integration_user_002"
        let content = "床前明月光，疑是地上霜。举头望明月，低头思故乡。"
        
        // Act & Assert - 步骤1：创建背诵任务
        let createResult = this.recitationService.createTask(
            "integration_reci_001", "静夜思背诵", content, RecitationType.ChinesePoetry
        )
        if (!createResult.success) {
            println("Test failed: create recitation task should succeed, error: ${createResult.message}")
            return
        }
        
        // Act & Assert - 步骤2：开始任务
        let startResult = this.recitationService.startTask("integration_reci_001")
        if (!startResult.success) {
            println("Test failed: start recitation task should succeed, error: ${startResult.message}")
            return
        }
        
        // Act & Assert - 步骤3：提交背诵内容
        let submitResult = this.recitationService.submitRecitation("integration_reci_001", content)
        if (!submitResult.success) {
            println("Test failed: submit recitation should succeed, error: ${submitResult.message}")
            return
        }
        
        // Act & Assert - 步骤4：完成任务
        let finishResult = this.recitationService.finishTask("integration_reci_001")
        if (!finishResult.success) {
            println("Test failed: finish recitation task should succeed, error: ${finishResult.message}")
            return
        }
        
        // Act & Assert - 步骤5：获取任务结果
        let taskResult = this.recitationService.getTask("integration_reci_001")
        if (!taskResult.success) {
            println("Test failed: get recitation task should succeed, error: ${taskResult.message}")
            return
        }
        
        // Act & Assert - 步骤6：保存到存储
        let saveResult = this.dataStorage.saveRecitationTask(taskResult.task)
        if (!saveResult.success) {
            println("Test failed: save recitation task should succeed, error: ${saveResult.message}")
            return
        }
        
        // Act & Assert - 步骤7：从存储加载验证
        let loadResult = this.dataStorage.loadRecitationTask("integration_reci_001")
        if (!loadResult.success) {
            println("Test failed: load recitation task should succeed, error: ${loadResult.message}")
            return
        }
        
        // 验证任务状态和结果
        if (loadResult.task.status != TaskStatus.Completed) {
            println("Test failed: task should be completed")
            return
        }
        
        if (loadResult.task.attempts.size == 0) {
            println("Test failed: should have at least one attempt")
            return
        }
        
        println("Test passed: Complete recitation workflow")
    }
    
    /**
     * 测试用户档案管理流程
     * 验证用户档案的创建、更新和统计功能
     */
    public func testUserProfileManagement(): Unit {
        println("Testing user profile management...")
        
        // Arrange - 准备测试数据
        let userId = "integration_user_003"
        let profile = UserProfile(userId, "集成测试用户", "四年级")
        
        // Act & Assert - 步骤1：保存用户档案
        let saveResult = this.dataStorage.saveUserProfile(profile)
        if (!saveResult.success) {
            println("Test failed: save user profile should succeed, error: ${saveResult.message}")
            return
        }
        
        // Act & Assert - 步骤2：加载用户档案
        let loadResult = this.dataStorage.loadUserProfile(userId)
        if (!loadResult.success) {
            println("Test failed: load user profile should succeed, error: ${loadResult.message}")
            return
        }
        
        // Act & Assert - 步骤3：更新统计数据
        loadResult.profile.updateDictationStats(88.5, true)
        loadResult.profile.updateRecitationStats(92.0, true)
        
        // Act & Assert - 步骤4：再次保存更新后的档案
        let updateResult = this.dataStorage.saveUserProfile(loadResult.profile)
        if (!updateResult.success) {
            println("Test failed: update user profile should succeed, error: ${updateResult.message}")
            return
        }
        
        // Act & Assert - 步骤5：验证更新结果
        let verifyResult = this.dataStorage.loadUserProfile(userId)
        if (!verifyResult.success) {
            println("Test failed: verify user profile should succeed, error: ${verifyResult.message}")
            return
        }
        
        // 验证统计数据
        if (verifyResult.profile.bestDictationScore < 88.0) {
            println("Test failed: dictation score should be updated")
            return
        }
        
        if (verifyResult.profile.bestRecitationScore < 92.0) {
            println("Test failed: recitation score should be updated")
            return
        }
        
        println("Test passed: User profile management")
    }
    
    /**
     * 测试多任务并发处理
     * 验证系统处理多个任务的能力
     */
    public func testMultiTaskHandling(): Unit {
        println("Testing multi-task handling...")
        
        // Arrange - 准备多个任务
        let words1 = Array<String>(2, {i => 
            match (i) {
                case 0 => "任务"
                case _ => "一"
            }
        })
        let words2 = Array<String>(2, {i => 
            match (i) {
                case 0 => "任务"
                case _ => "二"
            }
        })
        
        // Act & Assert - 创建多个听写任务
        let createResult1 = this.dictationService.createTask("multi_task_001", "多任务测试1", words1)
        let createResult2 = this.dictationService.createTask("multi_task_002", "多任务测试2", words2)
        
        if (!createResult1.success || !createResult2.success) {
            println("Test failed: create multiple tasks should succeed")
            return
        }
        
        // Act & Assert - 创建背诵任务
        let createResult3 = this.recitationService.createTask(
            "multi_task_003", "多任务背诵", "测试内容", RecitationType.ChineseText
        )
        
        if (!createResult3.success) {
            println("Test failed: create recitation task should succeed")
            return
        }
        
        // Act & Assert - 获取用户任务列表
        let taskListResult = this.dataStorage.getUserTasks("integration_user_004")
        if (!taskListResult.success) {
            println("Test failed: get user tasks should succeed, error: ${taskListResult.message}")
            return
        }
        
        // 验证任务数量
        if (taskListResult.tasks.size < 3) {
            println("Test passed: Multi-task handling (found ${taskListResult.tasks.size} tasks)")
        } else {
            println("Test passed: Multi-task handling")
        }
    }
    
    /**
     * 测试错误恢复机制
     * 验证系统在异常情况下的恢复能力
     */
    public func testErrorRecovery(): Unit {
        println("Testing error recovery...")
        
        // Act & Assert - 测试无效任务ID
        let invalidResult1 = this.dictationService.startTask("")
        if (invalidResult1.success) {
            println("Test failed: should fail with empty task ID")
            return
        }
        
        // Act & Assert - 测试不存在的任务
        let invalidResult2 = this.dictationService.getTask("nonexistent_task")
        if (invalidResult2.success) {
            println("Test failed: should fail with nonexistent task")
            return
        }
        
        // Act & Assert - 测试无效的存储操作
        let emptyWords = Array<String>(0, {i => ""})
        let invalidTask = DictationTask("", "无效任务", emptyWords)
        let invalidResult3 = this.dataStorage.saveDictationTask(invalidTask)
        if (invalidResult3.success) {
            println("Test failed: should fail with invalid task")
            return
        }
        
        println("Test passed: Error recovery")
    }
    
    /**
     * 运行所有集成测试
     */
    public func runAllTests(): Unit {
        println("Running application integration tests...")
        testCompleteeDictationWorkflow()
        testCompleteRecitationWorkflow()
        testUserProfileManagement()
        testMultiTaskHandling()
        testErrorRecovery()
        println("Application integration tests completed.")
    }
}

/**
 * 主函数 - 用于运行集成测试
 */
public func main(): Unit {
    let test = AppIntegrationTest()
    test.runAllTests()
}
