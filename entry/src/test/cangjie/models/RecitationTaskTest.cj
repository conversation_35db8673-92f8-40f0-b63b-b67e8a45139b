package ohos_app_cangjie_entry

import std.unittest.*
import std.collection.ArrayList
import std.time.DateTime

/**
 * 背诵任务数据模型测试
 * 
 * 测试理由：
 * 1. RecitationTask包含复杂的评分和尝试管理逻辑
 * 2. 需要验证最佳得分更新、平均分计算等业务规则
 * 3. 需要验证任务完成条件判断
 * 4. 需要验证多次尝试的记录管理
 */
@Test
class RecitationTaskTest {
    
    /**
     * 测试背诵任务的基本创建
     * 验证初始状态是否正确设置
     */
    @TestCase
    func testRecitationTaskCreation(): Unit {
        // Arrange & Act
        let task = RecitationTask("task001", "春晓背诵", "春眠不觉晓，处处闻啼鸟。", RecitationType.ChinesePoetry)
        
        // Assert
        @Assert.equal(task.id, "task001")
        @Assert.equal(task.title, "春晓背诵")
        @Assert.equal(task.content, "春眠不觉晓，处处闻啼鸟。")
        @Assert.equal(task.contentType, RecitationType.ChinesePoetry)
        @Assert.equal(task.status, RecitationStatus.NotStarted)
        @Assert.equal(task.attempts.size, 0)
        @Assert.equal(task.bestScore, 0.0)
    }
    
    /**
     * 测试开始背诵任务
     * 验证状态转换是否正确
     */
    @TestCase
    func testStartRecitationTask(): Unit {
        // Arrange
        let task = RecitationTask("task001", "测试背诵", "测试内容", RecitationType.ChineseText)
        
        // Act
        task.start()
        
        // Assert
        @Assert.equal(task.status, RecitationStatus.InProgress)
    }
    
    /**
     * 测试添加背诵尝试记录
     * 验证尝试记录添加和最佳得分更新
     */
    @TestCase
    func testAddAttempt(): Unit {
        // Arrange
        let task = RecitationTask("task001", "测试背诵", "测试内容", RecitationType.ChineseText)
        task.start()
        
        let errors = ArrayList<RecitationError>()
        let attempt = RecitationAttempt(
            "attempt001",
            "task001",
            DateTime.now(),
            DateTime.now(),
            30,
            80.0,  // accuracyScore
            75.0,  // fluencyScore
            85.0,  // completenessScore
            "测试内容",
            errors,
            "表现良好"
        )
        
        // Act
        task.addAttempt(attempt)
        
        // Assert
        @Assert.equal(task.attempts.size, 1)
        @Assert.equal(task.bestScore, 80.0)  // (80+75+85)/3 = 80
        @Assert.equal(task.status, RecitationStatus.InProgress)  // 得分未达到85，仍在进行中
    }
    
    /**
     * 测试高分尝试自动完成任务
     * 验证达到优秀标准时任务状态自动变为完成
     */
    @TestCase
    func testHighScoreAutoComplete(): Unit {
        // Arrange
        let task = RecitationTask("task001", "测试背诵", "测试内容", RecitationType.ChineseText)
        task.start()
        
        let errors = ArrayList<RecitationError>()
        let attempt = RecitationAttempt(
            "attempt001",
            "task001",
            DateTime.now(),
            DateTime.now(),
            30,
            90.0,  // accuracyScore
            88.0,  // fluencyScore
            92.0,  // completenessScore
            "测试内容",
            errors,
            "表现优秀"
        )
        
        // Act
        task.addAttempt(attempt)
        
        // Assert
        @Assert.equal(task.status, RecitationStatus.Completed)  // 得分90达到优秀标准，自动完成
        @Assert.equal(task.bestScore, 90.0)  // (90+88+92)/3 = 90
    }
    
    /**
     * 测试最佳得分更新
     * 验证多次尝试时最佳得分的正确更新
     */
    @TestCase
    func testBestScoreUpdate(): Unit {
        // Arrange
        let task = RecitationTask("task001", "测试背诵", "测试内容", RecitationType.ChineseText)
        task.start()
        
        let errors = ArrayList<RecitationError>()
        
        // 第一次尝试 - 得分70
        let attempt1 = RecitationAttempt(
            "attempt001", "task001", DateTime.now(), DateTime.now(), 30,
            70.0, 70.0, 70.0, "测试内容", errors, "需要改进"
        )
        
        // 第二次尝试 - 得分85
        let attempt2 = RecitationAttempt(
            "attempt002", "task001", DateTime.now(), DateTime.now(), 25,
            85.0, 85.0, 85.0, "测试内容", errors, "表现良好"
        )
        
        // 第三次尝试 - 得分75（比最佳得分低）
        let attempt3 = RecitationAttempt(
            "attempt003", "task001", DateTime.now(), DateTime.now(), 35,
            75.0, 75.0, 75.0, "测试内容", errors, "有所退步"
        )
        
        // Act
        task.addAttempt(attempt1)
        @Assert.equal(task.bestScore, 70.0)
        
        task.addAttempt(attempt2)
        @Assert.equal(task.bestScore, 85.0)  // 更新为更高分数
        
        task.addAttempt(attempt3)
        @Assert.equal(task.bestScore, 85.0)  // 保持最佳得分不变
        
        // Assert
        @Assert.equal(task.attempts.size, 3)
    }
    
    /**
     * 测试获取尝试次数
     * 验证尝试次数统计是否正确
     */
    @TestCase
    func testGetAttemptCount(): Unit {
        // Arrange
        let task = RecitationTask("task001", "测试背诵", "测试内容", RecitationType.ChineseText)
        
        // Act & Assert
        @Assert.equal(task.getAttemptCount(), 0)
        
        let errors = ArrayList<RecitationError>()
        let attempt = RecitationAttempt(
            "attempt001", "task001", DateTime.now(), DateTime.now(), 30,
            80.0, 80.0, 80.0, "测试内容", errors, "表现良好"
        )
        
        task.addAttempt(attempt)
        @Assert.equal(task.getAttemptCount(), 1)
    }
    
    /**
     * 测试获取最近一次尝试
     * 验证能否正确获取最新的尝试记录
     */
    @TestCase
    func testGetLatestAttempt(): Unit {
        // Arrange
        let task = RecitationTask("task001", "测试背诵", "测试内容", RecitationType.ChineseText)
        
        // Act & Assert - 无尝试记录时返回None
        @Assert.equal(task.getLatestAttempt(), None)
        
        let errors = ArrayList<RecitationError>()
        let attempt1 = RecitationAttempt(
            "attempt001", "task001", DateTime.now(), DateTime.now(), 30,
            70.0, 70.0, 70.0, "测试内容", errors, "第一次尝试"
        )
        let attempt2 = RecitationAttempt(
            "attempt002", "task001", DateTime.now(), DateTime.now(), 25,
            80.0, 80.0, 80.0, "测试内容", errors, "第二次尝试"
        )
        
        task.addAttempt(attempt1)
        let latest1 = task.getLatestAttempt()
        @Assert.notEqual(latest1, None)
        @Assert.equal(latest1.value.attemptId, "attempt001")
        
        task.addAttempt(attempt2)
        let latest2 = task.getLatestAttempt()
        @Assert.notEqual(latest2, None)
        @Assert.equal(latest2.value.attemptId, "attempt002")
    }
    
    /**
     * 测试平均得分计算
     * 验证多次尝试的平均分计算是否正确
     */
    @TestCase
    func testGetAverageScore(): Unit {
        // Arrange
        let task = RecitationTask("task001", "测试背诵", "测试内容", RecitationType.ChineseText)
        
        // Act & Assert - 无尝试记录时返回0
        @Assert.equal(task.getAverageScore(), 0.0)
        
        let errors = ArrayList<RecitationError>()
        let attempt1 = RecitationAttempt(
            "attempt001", "task001", DateTime.now(), DateTime.now(), 30,
            60.0, 60.0, 60.0, "测试内容", errors, "第一次"
        )  // 综合得分60
        
        let attempt2 = RecitationAttempt(
            "attempt002", "task001", DateTime.now(), DateTime.now(), 25,
            80.0, 80.0, 80.0, "测试内容", errors, "第二次"
        )  // 综合得分80
        
        task.addAttempt(attempt1)
        @Assert.equal(task.getAverageScore(), 60.0)
        
        task.addAttempt(attempt2)
        @Assert.equal(task.getAverageScore(), 70.0)  // (60+80)/2 = 70
    }
    
    /**
     * 测试是否需要更多练习
     * 验证练习需求判断逻辑
     */
    @TestCase
    func testNeedsMorePractice(): Unit {
        // Arrange
        let task = RecitationTask("task001", "测试背诵", "测试内容", RecitationType.ChineseText)
        
        // Act & Assert - 初始状态需要练习
        @Assert.equal(task.needsMorePractice(), true)
        
        let errors = ArrayList<RecitationError>()
        let lowScoreAttempt = RecitationAttempt(
            "attempt001", "task001", DateTime.now(), DateTime.now(), 30,
            70.0, 70.0, 70.0, "测试内容", errors, "需要改进"
        )  // 综合得分70，低于85
        
        let highScoreAttempt = RecitationAttempt(
            "attempt002", "task001", DateTime.now(), DateTime.now(), 25,
            90.0, 90.0, 90.0, "测试内容", errors, "表现优秀"
        )  // 综合得分90，高于85
        
        task.addAttempt(lowScoreAttempt)
        @Assert.equal(task.needsMorePractice(), true)  // 得分不够，需要继续练习
        
        task.addAttempt(highScoreAttempt)
        @Assert.equal(task.needsMorePractice(), false)  // 最佳得分达标，不需要更多练习
    }
}
