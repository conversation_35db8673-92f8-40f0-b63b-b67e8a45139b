package ohos_app_cangjie_entry.models

/**
 * 数据模型测试类
 * 用于验证核心数据模型的基本功能
 */
public class ModelTest {
    
    /**
     * 测试听写任务的基本功能
     */
    public func testDictationTaskBasics(): Unit {
        // Arrange
        let content = Array<String>(3, {i => 
            match (i) {
                case 0 => "苹果"
                case 1 => "香蕉"
                case _ => "橘子"
            }
        })
        
        // Act
        let task = DictationTask("task001", "水果听写", content)
        
        // Assert
        if (task.id != "task001") {
            println("Test failed: expected task001, got ${task.id}")
        } else if (task.title != "水果听写") {
            println("Test failed: expected 水果听写, got ${task.title}")
        } else if (task.content.size != 3) {
            println("Test failed: expected content size 3, got ${task.content.size}")
        } else {
            println("Test passed: DictationTask basics")
        }
    }
    
    /**
     * 测试听写任务的状态转换
     */
    public func testDictationTaskStateTransition(): Unit {
        // Arrange
        let content = Array<String>(2, {i => 
            match (i) {
                case 0 => "测试"
                case _ => "内容"
            }
        })
        let task = DictationTask("task002", "测试听写", content)
        
        // Act & Assert
        let canContinueInitial = match (task.status) {
            case DictationStatus.NotStarted => false
            case _ => true
        }
        
        if (canContinueInitial) {
            println("Test failed: should not be able to continue initially")
            return
        }
        
        task.start()
        let canContinueAfterStart = match (task.status) {
            case DictationStatus.InProgress => task.currentIndex < task.content.size
            case _ => false
        }
        
        if (!canContinueAfterStart) {
            println("Test failed: should be able to continue after start")
        } else {
            println("Test passed: DictationTask state transition")
        }
    }
    
    /**
     * 测试背诵任务的基本功能
     */
    public func testRecitationTaskBasics(): Unit {
        // Arrange & Act
        let task = RecitationTask("task003", "春晓背诵", "春眠不觉晓，处处闻啼鸟。", RecitationType.ChinesePoetry)
        
        // Assert
        if (task.id != "task003") {
            println("Test failed: expected task003, got ${task.id}")
        } else if (task.title != "春晓背诵") {
            println("Test failed: expected 春晓背诵, got ${task.title}")
        } else if (task.contentType != RecitationType.ChinesePoetry) {
            println("Test failed: content type mismatch")
        } else if (task.attempts.size != 0) {
            println("Test failed: expected 0 attempts, got ${task.attempts.size}")
        } else {
            println("Test passed: RecitationTask basics")
        }
    }
    
    /**
     * 测试用户档案的基本功能
     */
    public func testUserProfileBasics(): Unit {
        // Arrange & Act
        let profile = UserProfile("user001", "小明", "一年级")
        
        // Assert
        if (profile.userId != "user001") {
            println("Test failed: expected user001, got ${profile.userId}")
        } else if (profile.userName != "小明") {
            println("Test failed: expected 小明, got ${profile.userName}")
        } else if (profile.grade != "一年级") {
            println("Test failed: expected 一年级, got ${profile.grade}")
        } else if (profile.totalDictationTasks != 0) {
            println("Test failed: expected 0 dictation tasks, got ${profile.totalDictationTasks}")
        } else {
            println("Test passed: UserProfile basics")
        }
    }
    
    /**
     * 测试用户档案的统计更新
     */
    public func testUserProfileStatsUpdate(): Unit {
        // Arrange
        let profile = UserProfile("user002", "小红", "一年级")
        
        // Act
        profile.updateDictationStats(85.0, true)
        profile.updateRecitationStats(90.0, true)
        
        // Assert
        if (profile.totalDictationTasks != 1) {
            println("Test failed: expected 1 dictation task, got ${profile.totalDictationTasks}")
        } else if (profile.completedDictationTasks != 1) {
            println("Test failed: expected 1 completed dictation task, got ${profile.completedDictationTasks}")
        } else if (profile.bestDictationScore != 85.0) {
            println("Test failed: expected best dictation score 85.0, got ${profile.bestDictationScore}")
        } else if (profile.bestRecitationScore != 90.0) {
            println("Test failed: expected best recitation score 90.0, got ${profile.bestRecitationScore}")
        } else {
            println("Test passed: UserProfile stats update")
        }
    }
    
    /**
     * 测试枚举类型的使用
     */
    public func testEnumUsage(): Unit {
        // Test DictationStatus
        let status1 = DictationStatus.NotStarted
        let status2 = DictationStatus.InProgress
        
        let result1 = match (status1) {
            case DictationStatus.NotStarted => "not started"
            case DictationStatus.InProgress => "in progress"
            case DictationStatus.PendingReview => "pending review"
            case DictationStatus.Completed => "completed"
            case DictationStatus.Cancelled => "cancelled"
        }
        
        let result2 = match (status2) {
            case DictationStatus.NotStarted => "not started"
            case DictationStatus.InProgress => "in progress"
            case DictationStatus.PendingReview => "pending review"
            case DictationStatus.Completed => "completed"
            case DictationStatus.Cancelled => "cancelled"
        }
        
        if (result1 != "not started") {
            println("Test failed: expected 'not started', got '${result1}'")
        } else if (result2 != "in progress") {
            println("Test failed: expected 'in progress', got '${result2}'")
        } else {
            println("Test passed: Enum usage")
        }
    }
    
    /**
     * 运行所有模型测试
     */
    public func runAllTests(): Unit {
        println("Running model tests...")
        testDictationTaskBasics()
        testDictationTaskStateTransition()
        testRecitationTaskBasics()
        testUserProfileBasics()
        testUserProfileStatsUpdate()
        testEnumUsage()
        println("Model tests completed.")
    }
}

/**
 * 主函数 - 用于运行测试
 */
public func main(): Unit {
    let test = ModelTest()
    test.runAllTests()
}
