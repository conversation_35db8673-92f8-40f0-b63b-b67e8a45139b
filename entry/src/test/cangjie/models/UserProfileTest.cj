package ohos_app_cangjie_entry

import std.unittest.*
import std.collection.ArrayList
import std.time.DateTime

/**
 * 用户档案数据模型测试
 * 
 * 测试理由：
 * 1. UserProfile包含复杂的统计计算逻辑
 * 2. 需要验证学习记录更新、完成率计算等业务规则
 * 3. 需要验证用户偏好设置的管理
 * 4. 需要验证学习会话和成就系统的集成
 */
@Test
class UserProfileTest {
    
    /**
     * 测试用户档案的基本创建
     * 验证初始状态是否正确设置
     */
    @TestCase
    func testUserProfileCreation(): Unit {
        // Arrange & Act
        let profile = UserProfile("user001", "小明", "一年级")
        
        // Assert
        @Assert.equal(profile.userId, "user001")
        @Assert.equal(profile.userName, "小明")
        @Assert.equal(profile.grade, "一年级")
        @Assert.equal(profile.totalDictationTasks, 0)
        @Assert.equal(profile.completedDictationTasks, 0)
        @Assert.equal(profile.totalRecitationTasks, 0)
        @Assert.equal(profile.completedRecitationTasks, 0)
        @Assert.equal(profile.totalStudyTime, 0)
        @Assert.equal(profile.consecutiveStudyDays, 0)
        @Assert.equal(profile.lastStudyDate, None)
        @Assert.equal(profile.averageDictationScore, 0.0)
        @Assert.equal(profile.averageRecitationScore, 0.0)
        @Assert.equal(profile.bestDictationScore, 0.0)
        @Assert.equal(profile.bestRecitationScore, 0.0)
        @Assert.equal(profile.studyHistory.size, 0)
        @Assert.equal(profile.achievements.size, 0)
    }
    
    /**
     * 测试用户偏好设置的默认值
     * 验证偏好设置初始化是否正确
     */
    @TestCase
    func testUserPreferencesDefaults(): Unit {
        // Arrange & Act
        let profile = UserProfile("user001", "小明", "一年级")
        let prefs = profile.preferences
        
        // Assert
        @Assert.equal(prefs.speechSpeed, 1.0)
        @Assert.equal(prefs.speechVolume, 0.8)
        @Assert.equal(prefs.voiceType, "standard")
        @Assert.equal(prefs.enableSoundEffects, true)
        @Assert.equal(prefs.enableVibration, true)
        @Assert.equal(prefs.autoSaveProgress, true)
        @Assert.equal(prefs.reminderEnabled, false)
        @Assert.equal(prefs.reminderTime, "19:00")
    }
    
    /**
     * 测试更新听写统计
     * 验证听写任务统计更新是否正确
     */
    @TestCase
    func testUpdateDictationStats(): Unit {
        // Arrange
        let profile = UserProfile("user001", "小明", "一年级")
        
        // Act - 完成一个听写任务，得分85
        profile.updateDictationStats(85.0, true)
        
        // Assert
        @Assert.equal(profile.totalDictationTasks, 1)
        @Assert.equal(profile.completedDictationTasks, 1)
        @Assert.equal(profile.bestDictationScore, 85.0)
        
        // Act - 未完成一个听写任务，得分60
        profile.updateDictationStats(60.0, false)
        
        // Assert
        @Assert.equal(profile.totalDictationTasks, 2)
        @Assert.equal(profile.completedDictationTasks, 1)  // 完成数不变
        @Assert.equal(profile.bestDictationScore, 85.0)    // 最佳得分不变
        
        // Act - 完成一个更高分的听写任务，得分95
        profile.updateDictationStats(95.0, true)
        
        // Assert
        @Assert.equal(profile.totalDictationTasks, 3)
        @Assert.equal(profile.completedDictationTasks, 2)
        @Assert.equal(profile.bestDictationScore, 95.0)    // 最佳得分更新
    }
    
    /**
     * 测试更新背诵统计
     * 验证背诵任务统计更新是否正确
     */
    @TestCase
    func testUpdateRecitationStats(): Unit {
        // Arrange
        let profile = UserProfile("user001", "小明", "一年级")
        
        // Act - 完成一个背诵任务，得分90
        profile.updateRecitationStats(90.0, true)
        
        // Assert
        @Assert.equal(profile.totalRecitationTasks, 1)
        @Assert.equal(profile.completedRecitationTasks, 1)
        @Assert.equal(profile.bestRecitationScore, 90.0)
        
        // Act - 未完成一个背诵任务，得分70
        profile.updateRecitationStats(70.0, false)
        
        // Assert
        @Assert.equal(profile.totalRecitationTasks, 2)
        @Assert.equal(profile.completedRecitationTasks, 1)  // 完成数不变
        @Assert.equal(profile.bestRecitationScore, 90.0)    // 最佳得分不变
    }
    
    /**
     * 测试添加学习会话记录
     * 验证学习时长和历史记录更新
     */
    @TestCase
    func testAddStudySession(): Unit {
        // Arrange
        let profile = UserProfile("user001", "小明", "一年级")
        let session = StudySession(
            "session001",
            "user001",
            DateTime.now(),
            30,  // 30分钟
            TaskType.Dictation,
            3,   // 完成3个任务
            85.0 // 平均得分85
        )
        
        // Act
        profile.addStudySession(session)
        
        // Assert
        @Assert.equal(profile.studyHistory.size, 1)
        @Assert.equal(profile.totalStudyTime, 30)
        @Assert.equal(profile.consecutiveStudyDays, 1)  // 简化实现，每次+1
        @Assert.notEqual(profile.lastStudyDate, None)
    }
    
    /**
     * 测试听写完成率计算
     * 验证完成率计算逻辑是否正确
     */
    @TestCase
    func testGetDictationCompletionRate(): Unit {
        // Arrange
        let profile = UserProfile("user001", "小明", "一年级")
        
        // Act & Assert - 无任务时完成率为0
        @Assert.equal(profile.getDictationCompletionRate(), 0.0)
        
        // 添加一些统计数据
        profile.updateDictationStats(85.0, true)   // 完成
        profile.updateDictationStats(60.0, false)  // 未完成
        profile.updateDictationStats(90.0, true)   // 完成
        profile.updateDictationStats(70.0, false)  // 未完成
        profile.updateDictationStats(95.0, true)   // 完成
        
        // 总任务5个，完成3个，完成率应该是60%
        @Assert.equal(profile.getDictationCompletionRate(), 60.0)
    }
    
    /**
     * 测试背诵完成率计算
     * 验证完成率计算逻辑是否正确
     */
    @TestCase
    func testGetRecitationCompletionRate(): Unit {
        // Arrange
        let profile = UserProfile("user001", "小明", "一年级")
        
        // Act & Assert - 无任务时完成率为0
        @Assert.equal(profile.getRecitationCompletionRate(), 0.0)
        
        // 添加一些统计数据
        profile.updateRecitationStats(90.0, true)   // 完成
        profile.updateRecitationStats(75.0, false)  // 未完成
        profile.updateRecitationStats(88.0, true)   // 完成
        
        // 总任务3个，完成2个，完成率应该是66.67%
        let completionRate = profile.getRecitationCompletionRate()
        @Assert.equal(completionRate > 66.0 && completionRate < 67.0, true)
    }
    
    /**
     * 测试多个学习会话的时长累计
     * 验证学习时长统计是否正确
     */
    @TestCase
    func testMultipleStudySessions(): Unit {
        // Arrange
        let profile = UserProfile("user001", "小明", "一年级")
        
        let session1 = StudySession("session001", "user001", DateTime.now(), 20, TaskType.Dictation, 2, 80.0)
        let session2 = StudySession("session002", "user001", DateTime.now(), 30, TaskType.Recitation, 1, 85.0)
        let session3 = StudySession("session003", "user001", DateTime.now(), 25, TaskType.Mixed, 3, 90.0)
        
        // Act
        profile.addStudySession(session1)
        profile.addStudySession(session2)
        profile.addStudySession(session3)
        
        // Assert
        @Assert.equal(profile.studyHistory.size, 3)
        @Assert.equal(profile.totalStudyTime, 75)  // 20+30+25 = 75分钟
        @Assert.equal(profile.consecutiveStudyDays, 3)  // 简化实现，每次+1
    }
    
    /**
     * 测试成就记录创建
     * 验证成就系统基本功能
     */
    @TestCase
    func testAchievementCreation(): Unit {
        // Arrange & Act
        let achievement = Achievement(
            "ach001",
            "初学者",
            "完成第一个听写任务",
            "/icons/beginner.png",
            AchievementCategory.TaskCompletion
        )
        
        // Assert
        @Assert.equal(achievement.achievementId, "ach001")
        @Assert.equal(achievement.title, "初学者")
        @Assert.equal(achievement.description, "完成第一个听写任务")
        @Assert.equal(achievement.iconPath, "/icons/beginner.png")
        @Assert.equal(achievement.category, AchievementCategory.TaskCompletion)
    }
    
    /**
     * 测试学习会话记录创建
     * 验证学习会话数据结构
     */
    @TestCase
    func testStudySessionCreation(): Unit {
        // Arrange & Act
        let session = StudySession(
            "session001",
            "user001",
            DateTime.now(),
            45,  // 45分钟
            TaskType.Mixed,
            5,   // 完成5个任务
            87.5 // 平均得分87.5
        )
        
        // Assert
        @Assert.equal(session.sessionId, "session001")
        @Assert.equal(session.userId, "user001")
        @Assert.equal(session.duration, 45)
        @Assert.equal(session.taskType, TaskType.Mixed)
        @Assert.equal(session.tasksCompleted, 5)
        @Assert.equal(session.averageScore, 87.5)
    }
}
