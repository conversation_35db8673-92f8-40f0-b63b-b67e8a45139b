package ohos_app_cangjie_entry

import std.unittest.*
import std.collection.ArrayList
import std.time.DateTime

/**
 * 听写任务数据模型测试
 * 
 * 测试理由：
 * 1. DictationTask是核心业务模型，包含复杂的状态管理逻辑
 * 2. 需要验证任务状态转换的正确性
 * 3. 需要验证进度计算、答案提交等业务规则
 * 4. 需要验证边界条件和异常情况
 */
@Test
class DictationTaskTest {
    
    /**
     * 测试听写任务的基本创建
     * 验证初始状态是否正确设置
     */
    @TestCase
    func testDictationTaskCreation(): Unit {
        // Arrange
        let content = ArrayList<String>()
        content.append("苹果")
        content.append("香蕉")
        content.append("橘子")
        
        // Act
        let task = DictationTask("task001", "水果听写", content)
        
        // Assert
        @Assert.equal(task.id, "task001")
        @Assert.equal(task.title, "水果听写")
        @Assert.equal(task.content.size, 3)
        @Assert.equal(task.status, DictationStatus.NotStarted)
        @Assert.equal(task.currentIndex, 0)
        @Assert.equal(task.userAnswers.size, 0)
        @Assert.equal(task.score, 0.0)
        @Assert.equal(task.completedAt, None)
    }
    
    /**
     * 测试开始听写任务
     * 验证状态转换是否正确
     */
    @TestCase
    func testStartDictationTask(): Unit {
        // Arrange
        let content = ArrayList<String>()
        content.append("苹果")
        let task = DictationTask("task001", "测试听写", content)
        
        // Act
        task.start()
        
        // Assert
        @Assert.equal(task.status, DictationStatus.InProgress)
    }
    
    /**
     * 测试提交答案功能
     * 验证答案提交和索引更新是否正确
     */
    @TestCase
    func testSubmitAnswer(): Unit {
        // Arrange
        let content = ArrayList<String>()
        content.append("苹果")
        content.append("香蕉")
        let task = DictationTask("task001", "测试听写", content)
        task.start()
        
        // Act
        task.submitAnswer("苹果")
        
        // Assert
        @Assert.equal(task.userAnswers.size, 1)
        @Assert.equal(task.userAnswers[0], "苹果")
        @Assert.equal(task.currentIndex, 1)
        @Assert.equal(task.status, DictationStatus.InProgress)
    }
    
    /**
     * 测试提交最后一个答案
     * 验证状态是否正确转换为待批改
     */
    @TestCase
    func testSubmitLastAnswer(): Unit {
        // Arrange
        let content = ArrayList<String>()
        content.append("苹果")
        let task = DictationTask("task001", "测试听写", content)
        task.start()
        
        // Act
        task.submitAnswer("苹果")
        
        // Assert
        @Assert.equal(task.status, DictationStatus.PendingReview)
        @Assert.equal(task.currentIndex, 1)
    }
    
    /**
     * 测试获取当前内容
     * 验证能否正确获取当前需要听写的内容
     */
    @TestCase
    func testGetCurrentContent(): Unit {
        // Arrange
        let content = ArrayList<String>()
        content.append("苹果")
        content.append("香蕉")
        let task = DictationTask("task001", "测试听写", content)
        
        // Act & Assert
        let currentContent = task.getCurrentContent()
        @Assert.equal(currentContent, Some("苹果"))
        
        // 提交答案后获取下一个内容
        task.submitAnswer("苹果")
        let nextContent = task.getCurrentContent()
        @Assert.equal(nextContent, Some("香蕉"))
        
        // 提交最后一个答案后应该返回None
        task.submitAnswer("香蕉")
        let finalContent = task.getCurrentContent()
        @Assert.equal(finalContent, None)
    }
    
    /**
     * 测试进度计算
     * 验证进度百分比计算是否正确
     */
    @TestCase
    func testGetProgress(): Unit {
        // Arrange
        let content = ArrayList<String>()
        content.append("苹果")
        content.append("香蕉")
        content.append("橘子")
        content.append("葡萄")
        let task = DictationTask("task001", "测试听写", content)
        
        // Act & Assert
        @Assert.equal(task.getProgress(), 0.0)
        
        task.submitAnswer("苹果")
        @Assert.equal(task.getProgress(), 25.0)
        
        task.submitAnswer("香蕉")
        @Assert.equal(task.getProgress(), 50.0)
        
        task.submitAnswer("橘子")
        @Assert.equal(task.getProgress(), 75.0)
        
        task.submitAnswer("葡萄")
        @Assert.equal(task.getProgress(), 100.0)
    }
    
    /**
     * 测试空内容的进度计算
     * 验证边界条件处理
     */
    @TestCase
    func testGetProgressWithEmptyContent(): Unit {
        // Arrange
        let content = ArrayList<String>()
        let task = DictationTask("task001", "空测试", content)
        
        // Act & Assert
        @Assert.equal(task.getProgress(), 0.0)
    }
    
    /**
     * 测试是否可以继续听写
     * 验证业务逻辑判断是否正确
     */
    @TestCase
    func testCanContinue(): Unit {
        // Arrange
        let content = ArrayList<String>()
        content.append("苹果")
        content.append("香蕉")
        let task = DictationTask("task001", "测试听写", content)
        
        // Act & Assert
        @Assert.equal(task.canContinue(), false)  // 未开始状态不能继续
        
        task.start()
        @Assert.equal(task.canContinue(), true)   // 进行中状态可以继续
        
        task.submitAnswer("苹果")
        @Assert.equal(task.canContinue(), true)   // 还有内容可以继续
        
        task.submitAnswer("香蕉")
        @Assert.equal(task.canContinue(), false)  // 已完成所有内容，不能继续
    }
    
    /**
     * 测试完成批改功能
     * 验证批改结果设置是否正确
     */
    @TestCase
    func testCompleteReview(): Unit {
        // Arrange
        let content = ArrayList<String>()
        content.append("苹果")
        let task = DictationTask("task001", "测试听写", content)
        task.start()
        task.submitAnswer("苹果")
        
        let corrections = ArrayList<DictationCorrection>()
        let correction = DictationCorrection(0, "苹果", "苹果", true, ArrayList<String>())
        corrections.append(correction)
        
        // Act
        task.completeReview(corrections, 100.0)
        
        // Assert
        @Assert.equal(task.status, DictationStatus.Completed)
        @Assert.equal(task.score, 100.0)
        @Assert.equal(task.corrections.size, 1)
        @Assert.notEqual(task.completedAt, None)
    }
    
    /**
     * 测试超出范围的答案提交
     * 验证边界条件处理
     */
    @TestCase
    func testSubmitAnswerOutOfRange(): Unit {
        // Arrange
        let content = ArrayList<String>()
        content.append("苹果")
        let task = DictationTask("task001", "测试听写", content)
        task.start()
        task.submitAnswer("苹果")  // 提交第一个答案
        
        let initialAnswerCount = task.userAnswers.size
        let initialIndex = task.currentIndex
        
        // Act - 尝试提交超出范围的答案
        task.submitAnswer("多余答案")
        
        // Assert - 应该不会添加答案
        @Assert.equal(task.userAnswers.size, initialAnswerCount)
        @Assert.equal(task.currentIndex, initialIndex)
    }
}
