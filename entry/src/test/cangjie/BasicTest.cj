package ohos_app_cangjie_entry

/**
 * 基础测试类
 * 用于验证仓颉语言的基本语法和测试框架
 */
public class BasicTest {
    
    /**
     * 测试基本的字符串操作
     */
    public func testStringBasics(): Unit {
        let message = "Hello Cangjie"
        let expected = "Hello Cangjie"
        
        // 基本断言 - 使用简单的条件判断
        if (message != expected) {
            // 在实际的仓颉测试框架中，这里应该使用@Assert
            // 目前使用简单的错误输出
            println("Test failed: expected '${expected}', got '${message}'")
        } else {
            println("Test passed: String basics")
        }
    }
    
    /**
     * 测试数组操作
     */
    public func testArrayBasics(): Unit {
        let arr = Array<String>(3, {i => 
            match (i) {
                case 0 => "apple"
                case 1 => "banana"
                case _ => "orange"
            }
        })
        
        if (arr.size != 3) {
            println("Test failed: expected array size 3, got ${arr.size}")
        } else if (arr[0] != "apple") {
            println("Test failed: expected 'apple', got '${arr[0]}'")
        } else {
            println("Test passed: Array basics")
        }
    }
    
    /**
     * 测试枚举类型
     */
    public func testEnumBasics(): Unit {
        let status = TestStatus.InProgress
        
        let result = match (status) {
            case TestStatus.NotStarted => "not started"
            case TestStatus.InProgress => "in progress"
            case TestStatus.Completed => "completed"
        }
        
        if (result != "in progress") {
            println("Test failed: expected 'in progress', got '${result}'")
        } else {
            println("Test passed: Enum basics")
        }
    }
    
    /**
     * 测试类的基本功能
     */
    public func testClassBasics(): Unit {
        let task = SimpleTask("task001", "Test Task")
        
        if (task.getId() != "task001") {
            println("Test failed: expected 'task001', got '${task.getId()}'")
        } else if (task.getTitle() != "Test Task") {
            println("Test failed: expected 'Test Task', got '${task.getTitle()}'")
        } else {
            println("Test passed: Class basics")
        }
    }
    
    /**
     * 运行所有测试
     */
    public func runAllTests(): Unit {
        println("Running basic tests...")
        testStringBasics()
        testArrayBasics()
        testEnumBasics()
        testClassBasics()
        println("Basic tests completed.")
    }
}

/**
 * 测试用的枚举类型
 */
public enum TestStatus {
    | NotStarted
    | InProgress
    | Completed
}

/**
 * 测试用的简单类
 */
public class SimpleTask {
    private let id: String
    private let title: String
    
    public init(id: String, title: String) {
        this.id = id
        this.title = title
    }
    
    public func getId(): String {
        return this.id
    }
    
    public func getTitle(): String {
        return this.title
    }
}

/**
 * 主函数 - 用于运行测试
 */
public func main(): Unit {
    let test = BasicTest()
    test.runAllTests()
}
